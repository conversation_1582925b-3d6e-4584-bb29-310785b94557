/* ===========================
   主样式文件 - 性能优化版
   集成 Font Awesome 和 Tailwind CSS
   ========================== */

/* 性能优化 - 关键渲染路径优化 */
html {
    font-display: swap; /* 字体加载优化 */
    scroll-behavior: smooth;
    /* 确保滚动正常工作 */
    overflow-x: hidden;
    overflow-y: auto;
    height: 100%;
}

/* 全局样式 - 性能优化版 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
}

/* GPU加速和性能优化 */
*,
*::before,
*::after {
    will-change: auto;
    backface-visibility: hidden;
}

:root {
    /* 主题系统变量 - 通过主题管理器动态设置 */
    --primary-color: var(--primary-color);
    --primary-hover: var(--primary-hover);
    --primary-light: var(--primary-light);
    --primary-pale: #e3f2fd;
    --secondary-color: #e3f2fd;
    --background-color: var(--background-color);
    --surface-color: var(--text-inverse);
    --surface-hover: #f5f5f5;
    --text-color: var(--text-color);
    --text-light: var(--gray-600);
    --text-lighter: #9e9e9e;
    --text-secondary: var(--text-light);
    --text-inverse: #ffffff;
    --border-color: var(--border-color);
    --border-light: #f0f0f0;
    --success-color: #4caf50;
    --success-pale: #e8f5e8;
    --warning-color: #ff9800;
    --warning-pale: #fff3e0;
    --error-color: #f44336;
    --error-pale: #ffebee;
    --info-color: #2196f3;
    --info-pale: #e3f2fd;
    --shadow-color: var(--black-alpha-10);
    --shadow-hover: var(--black-alpha-15);
    --shadow-focus: rgba(33, 150, 243, 0.25);
    
    /* Alpha 颜色变量 - 由主题管理器动态生成 */
    
    /* 灰色系统 */
    --gray-50: #fafafa;
    --gray-100: #f5f5f5;
    --gray-200: var(--gray-200)eee;
    --gray-300: var(--gray-300);
    --gray-400: #bdbdbd;
    --gray-500: #9e9e9e;
    --gray-600: var(--gray-600);
    --gray-700: #616161;
    --gray-800: #424242;
    --gray-900: #212121;
    
    /* 状态颜色扩展 */
    --success-dark: var(--success-dark);
    --warning-dark: #f57c00;
    --error-dark: #d32f2f;
    --info-dark: var(--info-dark);
    
    /* 图表颜色 */
    --chart-primary: var(--primary-color);
    --chart-secondary: var(--warning-color);
    --chart-tertiary: var(--success-color);
    --chart-quaternary: #9c27b0;
    
    /* 字体变量 */
    --font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-2xl: 24px;
    --font-size-3xl: 32px;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* 间距变量 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-base: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    
    /* 圆角变量 */
    --radius-sm: 4px;
    --radius-base: 6px;
    --radius-lg: 8px;
    --radius-xl: 12px;
    --radius-full: 50%;
    
    /* 动画变量 */
    --animation-duration: 0.3s;
    --animation-easing: ease-in-out;
    
    /* 布局变量 */
    --sidebar-width: 240px;
    --header-height: 60px;
    
    /* 兼容旧变量名 */
    --hover-bg: var(--surface-hover);
    --active-bg: var(--active-bg, #e8f0fe);
    --active-text: var(--active-text, var(--primary-color));
    
    /* Google 品牌颜色 */
    --google-blue: #4285F4;
    --google-red: #EA4335;
    --google-yellow: #FBBC05;
    --google-green: #34A853;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    font-family: var(--font-family);
    /* 性能优化 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    /* 内容可见性优化 */
    contain: layout style paint;
    /* 确保滚动正常工作 */
    overflow-x: hidden;
    overflow-y: auto;
}

/* 应用整体布局 */
#app {
    display: flex;
    height: 100vh;
    width: 100%;
    background-color: var(--background-color);
    position: relative;
}

/* 侧边栏样式 */
.sidebar {
    width: var(--sidebar-width);
    height: 100vh;
    background-color: var(--surface-color);
    border-right: 1px solid var(--border-color);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.sidebar-logo {
    display: flex;
    align-items: center;
    padding: 16px;
    height: var(--header-height);
    border-bottom: 1px solid var(--border-color);
}

.sidebar-logo img {
    width: 32px;
    height: 32px;
    margin-right: 10px;
}

.sidebar-logo h1 {
    font-size: 16px;
    font-weight: 600;
}

.menu-section {
    margin-top: 20px;
    padding: 0 10px;
}

.menu-section h2 {
    font-size: 12px;
    color: var(--text-light);
    padding: 0 12px;
    margin-bottom: 8px;
    text-transform: uppercase;
}

.menu-item {
    display: flex;
    flex-direction: column;
    border-radius: 6px;
    cursor: pointer;
    margin-bottom: 4px;
    position: relative;
}

.menu-item-content {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    width: 100%;
}

.menu-item:hover .menu-item-content {
    background-color: var(--hover-bg);
}

.menu-item.active > .menu-item-content {
    background-color: var(--active-bg);
    color: var(--active-text);
    font-weight: 500;
    border-radius: 6px;
}

.menu-item i {
    margin-right: 10px;
    font-size: 18px;
}

/* 子菜单样式 */
.menu-item.has-submenu {
    position: relative;
}

.submenu-arrow {
    margin-left: auto;
    transition: transform 0.3s ease;
}

.menu-item.has-submenu.expanded .submenu-arrow {
    transform: rotate(180deg);
}

.submenu {
    display: none;
    padding: 6px 0 6px 16px;
    margin-top: 2px;
    margin-bottom: 2px;
    overflow: hidden;
    background: var(--surface-color);
    border-radius: var(--radius-base);
    border-left: 2px solid var(--border-light);
}

.menu-item.has-submenu.expanded .submenu {
    display: block;
    animation: submenuSlideIn 0.3s ease-out;
}

@keyframes submenuSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-8px);
        max-height: 0;
    }
    100% {
        opacity: 1;
        transform: translateY(0);
        max-height: 300px;
    }
}

.submenu-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: var(--radius-base);
    cursor: pointer;
    margin-bottom: 2px;
    font-size: 13px;
    transition: all var(--animation-duration) var(--animation-easing);
    position: relative;
}

.submenu-item:hover {
    background-color: var(--hover-bg);
    transform: translateX(2px);
}

.submenu-item.active {
    background-color: var(--active-bg);
    color: var(--active-text);
    font-weight: 500;
}

.submenu-item i {
    margin-right: 12px;
    font-size: 16px;
    transition: transform var(--animation-duration) var(--animation-easing);
}

.submenu-item:hover i {
    transform: scale(1.1);
}

.submenu-item.new-product {
    color: var(--primary-color);
    margin-top: 4px;
    font-weight: 500;
}

.submenu-item.new-product i {
    color: var(--primary-color);
}

.submenu-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, var(--border-color) 20%, var(--border-color) 80%, transparent 100%);
    margin: 10px 6px;
    opacity: 0.6;
}

/* GPT样式的对话历史区域 */
.chat-history-section {
    margin-top: 8px;
}

.submenu-section-title {
    font-size: 11px;
    font-weight: 600;
    color: var(--text-tertiary);
    text-transform: uppercase;
    letter-spacing: 0.8px;
    padding: 8px 12px 6px 12px;
    margin-bottom: 6px;
    position: relative;
}

.submenu-section-title::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 12px;
    width: 20px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: 1px;
    opacity: 0.8;
}

.chat-item {
    position: relative;
    padding: 8px 10px !important;
    margin-bottom: 2px !important;
    border-radius: var(--radius-base);
    transition: all var(--animation-duration) var(--animation-easing);
    cursor: pointer;
}

.chat-item-content {
    display: flex;
    align-items: center;
    min-width: 0;
    flex: 1;
    transition: padding-right var(--animation-duration) var(--animation-easing);
}

.chat-title {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-left: 10px;
    transition: color var(--animation-duration) var(--animation-easing);
}

.chat-action-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 18px;
    height: 18px;
    border: none;
    background: none;
    color: var(--text-tertiary);
    cursor: pointer;
    opacity: 0;
    transition: all var(--animation-duration) var(--animation-easing);
    border-radius: var(--radius-xs);
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-action-btn:hover {
    background: var(--error-alpha-90);
    color: var(--error-color);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 2px 6px var(--error-alpha-90);
}

.chat-item:hover .chat-action-btn {
    opacity: 1;
}

.chat-item:hover .chat-item-content {
    padding-right: 32px;
}

.chat-item.active {
    background: var(--primary-alpha-15);
    border-left: 3px solid var(--primary-color);
    padding-left: 9px !important;
    box-shadow: 0 1px 3px var(--primary-alpha-10);
}

.chat-item.active .chat-title {
    color: var(--primary-color);
    font-weight: 600;
}

.chat-item:hover:not(.active) {
    background: var(--hover-bg);
    transform: translateX(2px);
}

.chat-item i {
    font-size: 16px;
    color: var(--text-secondary);
    transition: color var(--animation-duration) var(--animation-easing);
}

.chat-item.active i {
    color: var(--primary-color);
}

.chat-item:hover:not(.active) i {
    color: var(--text-primary);
}



.new-chat {
    background: var(--surface-color) !important;
    border: 1px dashed var(--border-color) !important;
    color: var(--text-secondary) !important;
    font-weight: 500 !important;
    padding: 10px 12px !important;
    margin: 4px 0 8px 0 !important;
    border-radius: var(--radius-base) !important;
    transition: all var(--animation-duration) var(--animation-easing) !important;
}

.new-chat:hover {
    background: var(--primary-alpha-05) !important;
    border-color: var(--primary-color) !important;
    color: var(--primary-color) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px var(--primary-alpha-15) !important;
}

.new-chat i {
    margin-right: 10px !important;
    font-size: 16px !important;
    transition: transform var(--animation-duration) var(--animation-easing) !important;
}

.new-chat:hover i {
    transform: scale(1.1) !important;
}

.menu-item .counter {
    position: absolute;
    right: 12px;
    background-color: var(--gray-300);
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 12px;
}

.user-profile {
    margin-top: auto;
    padding: 0;
    display: flex;
    flex-direction: column;
    border-top: 1px solid var(--border-color);
    position: relative;
    z-index: 100; /* 确保用户信息区域在高层级 */
}

.user-profile-clickable {
    display: flex;
    align-items: center;
    padding: 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    position: relative; /* 添加相对定位，使下拉菜单能正确定位 */
}

.user-profile-clickable:hover {
    background-color: var(--hover-bg);
}

.dropdown-arrow {
    margin-left: auto;
    color: var(--text-light);
    font-size: 18px;
}

.sidebar-avatar-container {
    position: relative;
    cursor: pointer;
}

.user-profile img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 10px;
    transition: all 0.2s ease;
}

.sidebar-avatar-container:hover img {
    transform: scale(1.1);
    box-shadow: 0 0 0 2px var(--primary-alpha-20);
}

.user-profile .user-info {
    display: flex;
    flex-direction: column;
}

.user-profile .user-name {
    font-size: 14px;
    font-weight: 500;
}

.user-profile .user-email {
    font-size: 12px;
    color: var(--text-light);
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    display: flex;
    flex-direction: column;
    height: 100vh; /* 改为固定高度而不是最小高度 */
    position: relative;
    z-index: 1;
    background-color: var(--background-color);
    width: calc(100vw - var(--sidebar-width));
    min-width: 0; /* 允许收缩 */
    overflow: hidden; /* 防止主容器滚动 */
}

/* 顶部导航栏 */
.header {
    height: var(--header-height);
    display: flex;
    align-items: center;
    padding: 0 20px;
    background-color: var(--surface-color);
    border-bottom: 1px solid var(--border-color);
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.product-title {
    font-size: 18px;
    font-weight: 600;
}

.search-bar {
    flex: 1;
    max-width: 600px;
    margin: 0 20px;
}

.search-input {
    width: 100%;
    padding: 8px 16px;
    border-radius: 20px;
    border: 1px solid var(--border-color);
    background-color: var(--gray-100);
    font-size: 14px;
}

.user-actions {
    display: flex;
    align-items: center;
}

.user-actions .icon-button {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
    cursor: pointer;
}

.user-actions .icon-button:hover {
    background-color: var(--hover-bg);
}

.user-actions .user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    margin-left: 16px;
    cursor: pointer;
}

/* 用户头像下拉菜单样式 */
.user-profile-dropdown {
    position: relative;
    display: flex;
    align-items: center;
}

.settings-button {
    margin-left: 4px;
    background-color: transparent;
    transition: all 0.2s ease;
}

.settings-button:hover {
    background-color: var(--hover-bg);
    transform: rotate(30deg);
}

.user-dropdown-menu, .sidebar-dropdown-menu {
    position: absolute;
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px var(--shadow-hover);
    min-width: 180px;
    z-index: 1000; /* 增加z-index确保菜单显示在最上层 */
    overflow: hidden;
    display: none;
}

.user-dropdown-menu {
    top: calc(100% + 8px);
    right: 0;
}

.sidebar-dropdown-menu {
    bottom: calc(100% + 5px); /* 改为向上弹出，并添加一点间距 */
    left: 0;
    width: 100%;
    position: absolute;
    z-index: 1001; /* 确保在最上层 */
    box-shadow: 0 -4px 16px var(--shadow-color); /* 调整阴影方向 */
    border: 1px solid  var(--border-color);
    border-bottom: none; /* 移除底部边框 */
    border-radius: 8px 8px 0 0; /* 调整圆角，只有上方有圆角 */
    background-color: var(--surface-color);
}

.user-dropdown-menu.show {
    display: block !important; /* 使用!important确保显示 */
    animation: fadeIn 0.2s ease;
    opacity: 1;
    visibility: visible;
}

.sidebar-dropdown-menu.show {
    display: block !important; /* 使用!important确保显示 */
    animation: fadeInUp 0.2s ease; /* 使用向上弹出的动画 */
    opacity: 1;
    visibility: visible;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 为侧边栏下拉菜单添加向上弹出的动画 */
@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap; /* 防止文本换行 */
}

.dropdown-item:hover {
    background-color: var(--hover-bg);
}

.dropdown-item i {
    margin-right: 10px;
    font-size: 18px;
    color: var(--text-light);
}

.dropdown-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 4px 0;
}

/* 通知中心样式 */
.notification-container {
    position: relative;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--error-color);
    color: var(--text-inverse);
    font-size: 11px;
    font-weight: bold;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--surface-color);
}

.notification-dropdown {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px var(--shadow-hover);
    width: 360px;
    max-height: 480px;
    z-index: 1010;
    overflow: hidden;
    display: none;
}

.notification-dropdown.show {
    display: block;
    animation: fadeIn 0.2s ease;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
}

.notification-header h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.notification-actions {
    font-size: 13px;
    color: var(--primary-color);
    cursor: pointer;
}

.notification-list {
    max-height: 360px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-light);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.notification-item:hover {
    background-color: var(--background-color);
}

.notification-item.unread {
    background-color: var(--info-bg);
}

.notification-item.unread:hover {
    background-color: var(--primary-pale);
}

.notification-avatar {
    margin-right: 12px;
}

.notification-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 500;
    margin-bottom: 4px;
    font-size: 14px;
}

.notification-text {
    font-size: 13px;
    color: var(--text-secondary);
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-clamp: 2;
}

.notification-time {
    font-size: 12px;
    color: var(--text-tertiary);
}

.notification-footer {
    padding: 12px 16px;
    text-align: center;
    border-top: 1px solid var(--border-color);
}

.view-all-notifications {
    color: var(--primary-color);
    font-size: 14px;
    text-decoration: none;
}

.view-all-notifications:hover {
    text-decoration: underline;
}

/* 通知弹窗样式 */
.notifications-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--overlay);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--surface-color);
    border-radius: 12px;
    box-shadow: 0 8px 24px var(--shadow-hover);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 14px 16px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--surface-color);
}

.modal-header h2 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-color);
}

.close-modal-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-modal-btn:hover {
    background-color: var(--border-light);
    color: var(--text-color);
}

.modal-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--background-alt);
    padding: 0;
}

.modal-tab {
    padding: 8px 16px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 2px solid transparent;
    position: relative;
    text-align: center;
    flex: 1;
}

.modal-tab.active {
    border-bottom-color: var(--primary-color);
    color: var(--primary-color);
    background-color: var(--surface-color);
    font-weight: 600;
}

.modal-tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--primary-color);
}

.modal-tab:hover:not(.active) {
    background-color: var(--border-light);
}

.notifications-filter {
    display: flex;
    padding: 8px 12px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--background-alt);
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 8px;
}

.filter-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 6px;
}

.filter-label {
    font-size: 12px;
    color: var(--text-secondary);
    white-space: nowrap;
    font-weight: 500;
}

.filter-select {
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    font-size: 12px;
    background-color: var(--surface-color);
    height: 28px;
    box-shadow: 0 1px 2px var(--shadow-color);
}

.mark-all-read-btn {
    padding: 4px 10px;
    background-color: var(--info-bg);
    color: var(--primary-color);
    border: 1px solid var(--primary-light);
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
    height: 28px;
    white-space: nowrap;
    font-weight: 500;
    box-shadow: 0 1px 2px var(--primary-alpha-10);
}

.mark-all-read-btn:hover {
    background-color: var(--primary-pale);
    box-shadow: 0 2px 4px var(--primary-alpha-15);
    transform: translateY(-1px);
}

.notifications-list, .activities-list {
    flex: 1;
    overflow-y: auto;
    padding: 4px;
    max-height: 70vh;
    background-color: var(--background-color);
}

.notification-item-full, .activity-item-full {
    display: flex;
    padding: 8px 10px;
    border-bottom: 1px solid var(--border-light);
    transition: all 0.2s ease;
    align-items: flex-start;
    border-radius: 6px;
    margin: 4px 6px;
}

.notification-item-full:hover, .activity-item-full:hover {
    background-color: var(--surface-hover);
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    transform: translateY(-1px);
}

.notification-item-full.unread {
    background-color: var(--info-bg);
    box-shadow: 0 1px 3px var(--primary-alpha-10);
}

.notification-item-full.unread:hover {
    background-color: var(--primary-pale);
    box-shadow: 0 2px 5px var(--primary-alpha-15);
}

.notification-icon, .activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    flex-shrink: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.notification-icon.reply, .activity-icon.mail {
    background-color: var(--primary-pale);
    color: var(--primary-color);
}

.notification-icon.confirm, .activity-icon.confirm {
    background-color: var(--success-pale);
    color: var(--success-dark);
}

.notification-icon.publish, .activity-icon.publish {
    background-color: var(--error-pale);
    color: var(--error-dark);
}

.notification-icon.system, .activity-icon.system {
    background-color: var(--warning-bg, var(--text-inverse)de7);
    color: var(--warning-dark);
}

.activity-icon.user-action {
    background-color: var(--info-pale);
    color: #3949ab;
}

/* 优化通知项样式 */
.notification-item-full {
    border-left: 3px solid transparent;
}

.notification-item-full[data-type="reply"] {
    border-left-color: var(--primary-color);
}

.notification-item-full[data-type="confirm"] {
    border-left-color: var(--success-dark);
}

.notification-item-full[data-type="publish"] {
    border-left-color: var(--error-dark);
}

.notification-item-full[data-type="system"] {
    border-left-color: var(--warning-dark);
}

.notification-icon i, .activity-icon i {
    font-size: 16px;
}

.notification-content-full, .activity-content-full {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.notification-header-row, .activity-header-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2px;
}

.notification-title-full, .activity-title-full {
    font-weight: 600;
    font-size: 13px;
    line-height: 1.3;
    flex: 1;
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--text-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.notification-title-full .creator-name, .activity-title-full .creator-name {
    color: var(--primary-color);
}

/* 移除通知标题前的图标 */

.notification-time-full, .activity-time-full {
    font-size: 11px;
    color: var(--text-secondary);
    white-space: nowrap;
    margin-left: 6px;
    display: flex;
    align-items: center;
    background-color: var(--gray-100);
    padding: 1px 6px;
    border-radius: 10px;
}

.activity-time-full i, .notification-time-full i {
    font-size: 10px;
    margin-right: 1px;
}

.notification-message {
    font-size: 12px;
    color: var(--text-primary);
    line-height: 1.3;
    margin-bottom: 6px;
    padding: 6px 8px;
    border-radius: 6px;
    background-color: var(--surface-color);
    border-left: 3px solid  var(--border-color);
    max-height: 60px;
    overflow-y: auto;
    box-shadow: 0 1px 2px var(--shadow-color) inset;
}

.activity-details {
    font-size: 12px;
    color: var(--text-primary);
    line-height: 1.3;
    margin-bottom: 6px;
    padding: 0;
    max-height: none;
    overflow: visible;
}

.activity-details > div {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    background-color: var(--surface-color);
    padding: 4px 8px;
    border-radius: 6px;
}

.activity-details i {
    font-size: 12px;
    color: var(--text-secondary);
    margin-right: 4px;
}

.notification-content-full, .activity-content-full {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.notification-actions, .activity-actions {
    display: flex;
    gap: 6px;
    margin-top: 4px;
    justify-content: flex-end;
    padding-top: 4px;
    border-top: 1px dashed var(--border-light);
}

.action-btn {
    padding: 3px 8px;
    border-radius: 6px;
    font-size: 11px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
    border: 1px solid  var(--border-color);
    background-color: var(--surface-color);
    color: var(--text-secondary);
    height: 24px;
    box-shadow: 0 1px 2px var(--shadow-color);
}

.action-btn:hover {
    background-color: var(--gray-100);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.action-btn.primary {
    background-color: var(--primary-color);
    color: var(--text-inverse);
    border-color: var(--primary-color);
    font-weight: 500;
}

.action-btn.primary:hover {
    background-color: var(--primary-dark);
    box-shadow: 0 2px 4px var(--primary-alpha-20);
}

.action-btn i {
    font-size: 12px;
}

.modal-footer {
    padding: 10px 16px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: center;
    background-color: var(--background-alt);
}

.pagination {
    display: flex;
    align-items: center;
    gap: 12px;
}

.pagination-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid  var(--border-color);
    background-color: var(--surface-color);
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.pagination-btn:hover {
    background-color: var(--gray-100);
    transform: translateY(-1px);
    box-shadow: 0 2px 5px var(--shadow-color);
}

.pagination-info {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* 内容页面 */
.content-area {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    overflow-x: hidden; /* 防止水平滚动 */
    width: 100%;
    box-sizing: border-box; /* 确保padding不增加宽度 */
    height: calc(100vh - var(--header-height)); /* 明确设置高度 */
}

/* 仪表盘近期活动样式 */
.recent-activities {
    background-color: var(--surface-color);
    border-radius: 12px;
    box-shadow: 0 2px 8px var(--shadow-color);
    padding: 16px;
    margin-bottom: 24px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
}

.section-header h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.view-all-link {
    font-size: 14px;
    color: var(--primary-color);
    text-decoration: none;
}

.view-all-link:hover {
    text-decoration: underline;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    padding: 10px 12px;
    border-radius: 8px;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.activity-item:hover {
    background-color: var(--gray-100);
}

.activity-icon {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    flex-shrink: 0;
}

.activity-icon.reply, .activity-icon.mail {
    background-color: var(--primary-pale);
    color: var(--primary-color);
}

.activity-icon.confirm {
    background-color: var(--success-pale);
    color: var(--success-dark);
}

.activity-icon.publish {
    background-color: var(--error-pale);
    color: var(--error-dark);
}

.activity-icon.update {
    background-color: var(--info-pale);
    color: #3949ab;
}

.activity-icon i {
    font-size: 14px;
}

.activity-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.activity-title {
    font-size: 13px;
    font-weight: 600;
    margin-bottom: 3px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.activity-type-badge, .notification-type-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    height: 18px;
    min-width: 36px;
    vertical-align: middle;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    margin-right: 6px;
}

.activity-type-badge.email, .notification-type-badge.reply {
    background-color: var(--primary-pale);
    color: var(--primary-color);
}

.activity-type-badge.update {
    background-color: var(--info-pale);
    color: #3949ab;
}

.activity-type-badge.product {
    background-color: var(--success-pale);
    color: var(--success-dark);
}

.notification-type-badge.confirm {
    background-color: var(--success-pale);
    color: var(--success-dark);
}

.notification-type-badge.publish {
    background-color: var(--error-pale);
    color: var(--error-dark);
}

.notification-type-badge.system {
    background-color: var(--warning-bg, var(--text-inverse)de7);
    color: var(--warning-dark);
}

.activity-time {
    font-size: 11px;
    color: var(--gray-600);
    white-space: nowrap;
    margin-left: 8px;
}

/* 产品库页面样式 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.page-title {
    font-size: 24px;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    display: flex;
    align-items: center;
    gap: 6px;
}

.btn-primary, #save-product {
    background-color: var(--primary-color);
    color: var(--text-inverse);
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px var(--primary-alpha-20);
}

.btn-primary:hover, #save-product:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px var(--primary-alpha-30);
}

.btn-outline, #cancel-edit {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-outline:hover, #cancel-edit:hover {
    background-color: var(--gray-100);
    border-color: var(--gray-400);
    color: var(--text-color);
}

.search-filter {
    display: flex;
    margin-bottom: 20px;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.search-input {
    flex: 1;
    min-width: 280px;
    padding: 10px 16px;
    border-radius: 10px;
    border: 1px solid  var(--border-color);
    font-size: 14px;
    background-color: var(--gray-50);
    box-shadow: 0 2px 4px rgba(0,0,0,0.03);
    transition: all 0.25s ease;
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-alpha-15);
    background-color: var(--surface-color);
    outline: none;
}

.search-input::placeholder {
    color: var(--gray-400);
    font-size: 13px;
}

.filter-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 18px;
    margin-top: 20px;
}

.product-grid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-light);
}

.grid-view-options {
    display: flex;
    gap: 8px;
}

.view-option-btn {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--surface-color);
    border: 1px solid  var(--border-color);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-option-btn:hover, .view-option-btn.active {
    background-color: var(--primary-color);
    color: var(--text-color);
    border-color: var(--primary-color);
}

.product-sort-by {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--text-secondary);
    position: relative;
}

.sort-label {
    font-weight: 500;
    color: var(--text-secondary);
}

.sort-select-wrapper {
    position: relative;
    display: inline-block;
}

.sort-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    pointer-events: none;
}

.sort-select {
    padding: 8px 32px 8px 12px;
    border-radius: 8px;
    border: 1px solid  var(--border-color);
    font-size: 14px;
    background-color: var(--surface-color);
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    min-width: 140px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.sort-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-alpha-10);
}

.sort-select:hover {
    border-color: var(--gray-400);
    background-color: var(--gray-50);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 14px;
    }

    .search-filter {
        flex-direction: column;
        align-items: stretch;
    }

    .search-input {
        min-width: 100%;
    }

    .filter-controls {
        justify-content: space-between;
    }

    /* 建联记录移动端优化 */
    .outreach-header {
        flex-direction: column;
        gap: 12px;
        padding: 12px 16px;
    }

    .outreach-item {
        flex-direction: column;
        align-items: stretch;
        padding: 12px;
        gap: 12px;
    }

    .outreach-creator {
        flex: none;
        min-width: auto;
    }

    .outreach-product,
    .outreach-intent {
        flex: none;
        border-left: none;
        border-top: 1px solid #f0f0f0;
        padding: 8px 0;
        min-width: auto;
    }

    .outreach-product:first-of-type {
        border-top: none;
    }

    /* 建联记录详情侧边栏移动端适配 */
    .outreach-detail {
        width: 100vw;
        left: 0;
        right: 0;
    }

    /* 详情页滚动容器移动端适配 */
    .detail-scroll-container {
        padding: 16px;
        gap: 16px;
    }

    /* 移动端达人和商品信息布局调整 */
    .creator-product-info {
        flex-direction: column;
        gap: 20px;
        min-height: auto;
    }

    .creator-section {
        gap: 16px;
    }

    .creator-avatar-large {
        width: 70px;
        height: 70px;
        border-radius: 16px;
    }

    .detail-creator-name {
        font-size: 16px;
    }

    .detail-creator-stats {
        gap: 8px;
    }

    .detail-creator-stats span {
        padding: 3px 6px;
        font-size: 12px;
    }

    .creator-tags {
        gap: 6px;
        margin-top: 8px;
    }

    .creator-tag {
        padding: 4px 8px;
        font-size: 12px;
    }

    .product-section {
        padding-left: 0;
        border-left: none;
        border-top: 2px solid var(--border-color);
        padding-top: 16px;
    }

    .product-section .product-img {
        width: 60px;
        height: 60px;
        border-radius: 12px;
    }

    /* 状态指示器移动端适配 */
    .outreach-stage-indicator,
    .email-intent-indicator {
        padding: 16px;
    }

    .stage-progress {
        gap: 8px;
        padding: 4px 0;
    }

    .stage-item {
        min-width: 60px;
    }

    .stage-label {
        font-size: 11px;
    }

    .stage-dot {
        width: 20px;
        height: 20px;
    }

    .stage-item.completed .stage-dot::after {
        font-size: 10px;
    }

    .intent-badge {
        font-size: 13px;
        padding: 5px 10px;
    }

    .intent-description {
        font-size: 13px;
    }

    /* 沟通记录和近期内容移动端适配 */
    .communication-content,
    .recent-content-section {
        padding: 16px;
    }

    .communication-header h4,
    .content-header h4 {
        font-size: 15px;
    }

    .product-section .product-name {
        font-size: 14px;
    }

    .product-section .product-description {
        font-size: 13px;
        padding: 4px 8px;
    }

    .collaboration-details {
        gap: 12px;
        padding-top: 12px;
    }

    /* 移动端邮件内容调整 */
    .email-content {
        padding: 12px;
    }

    .email-content .original-text {
        padding-right: 35px;
        font-size: 13px;
    }

    .translate-btn {
        width: 28px;
        height: 28px;
        font-size: 14px;
        top: 8px;
        right: 8px;
    }
}

.product-card {
    background-color: var(--surface-color);
    border-radius: 20px;
    border: 1px solid var(--border-color);
    overflow: visible; /* 修改为visible，允许菜单显示 */
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    box-shadow: 0 4px 12px var(--shadow-color);
}

.product-card:hover {
    box-shadow: 0 12px 24px var(--shadow-hover);
    transform: translateY(-5px);
    border-color: var(--primary-alpha-30);
}

/* 产品卡片图片样式 */

.product-img {
    height: 160px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--background-color);
    position: relative;
    overflow: hidden;
    border-bottom: 1px solid var(--border-light);
    border-radius: 20px 20px 0 0;
    z-index: 1; /* 保持较低的z-index，确保蓝色顶条在上方 */
    /* 添加clip-path确保圆角效果 */
    clip-path: inset(0 0 0 0 round 20px 20px 0 0);
}

.product-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .product-img img {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background: var(--primary-alpha-90);
    color: var(--text-color);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    z-index: 1;
}

.product-badge.new {
    background: var(--success-alpha-90);
}

.product-badge.sale {
    background: var(--error-alpha-90);
}

.product-info {
    padding: 16px;
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 5;
    /* 确保内容不超出边界 */
    overflow: hidden;
}

.product-name {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    color: var(--text-color);
    line-height: 1.4;
    letter-spacing: -0.3px;
}

.product-category {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    background-color: var(--background-color);
    border-radius: 20px;
    padding: 6px 10px;
    text-align: center;
    position: relative;
    z-index: 5;
}

.category-tag {
    background-color: var(--primary-pale);
    color: var(--primary-color);
    padding: 4px 8px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 2px var(--shadow-color);
    min-width: 60px;
    text-align: center;
}

.category-tag i {
    margin-right: 4px;
    font-size: 12px;
}

.category-tag:hover {
    background-color: var(--primary-light);
    transform: translateY(-2px);
}

/* 价格标签样式 */
.price-tag {
    background-color: var(--success-pale);
    color: var(--success-dark);
    font-weight: 600;
    padding: 4px 10px;
    border-radius: 20px;
    box-shadow: 0 2px 4px var(--shadow-color);
    border: 1px solid var(--success-alpha-10);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 70px;
    text-align: center;
    font-size: 12px;
}

.price-tag i {
    margin-right: 4px;
    font-size: 12px;
    color: var(--success-dark);
}

.price-tag:hover {
    background-color: var(--success-light);
    transform: translateY(-2px);
}

.product-description {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: 14px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    flex: 1;
    background-color: var(--gray-50);
    padding: 10px;
    border-radius: 12px;
    border-left: 3px solid var(--primary-alpha-30);
}

.product-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-bottom: 14px;
    padding: 0 2px;
    justify-content: center;
}

.product-tag {
    background-color: var(--info-bg);
    color: var(--info-dark);
    padding: 4px 8px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    box-shadow: 0 1px 3px var(--shadow-color);
    border: 1px solid var(--info-alpha-10);
    transition: all 0.2s ease;
    text-align: center;
    margin: 2px;
}

.product-tag:hover {
    background-color: var(--info-light);
    transform: translateY(-2px);
    box-shadow: 0 2px 5px var(--black-alpha-10);
}

.product-tag i {
    margin-right: 4px;
    font-size: 12px;
    color: var(--info-dark);
}

.product-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    background-color: var(--background-color);
    border-radius: 12px;
    padding: 8px;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    position: relative;
}

.stat-item:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 0;
    top: 15%;
    height: 70%;
    width: 1px;
    background-color: var(--gray-300);
}

.stat-value {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 11px;
    color: var(--text-light);
    text-align: center;
}

.product-actions {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px 16px;
    border-top: 1px solid var(--border-color);
    background-color: var(--surface-color);
    margin-top: auto;
    position: relative; /* 添加相对定位 */
    z-index: 5; /* 适当的z-index，确保在卡片内部 */
    border-radius: 0 0 20px 20px; /* 添加底部圆角 */
    overflow: visible; /* 修改为visible，允许菜单显示 */
}

.product-owner {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: var(--text-secondary);
    transition: all 0.2s ease;
}

.product-owner:hover {
    color: var(--primary-color);
}

.product-owner img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 8px;
    border: 2px solid var(--surface-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chat-btn {
    padding: 12px 24px;
    border-radius: 20px;
    background: linear-gradient(135deg, var(--primary-color), #64b5f6);
    color: var(--text-color);
    border: none;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px var(--primary-alpha-20);
    width: 100%;
    position: relative; /* 添加相对定位 */
    z-index: 1; /* 确保在底部圆角内部 */
}

.chat-btn i {
    margin-right: 8px;
    font-size: 18px;
}

.chat-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 16px var(--primary-alpha-30);
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-light));
}

.action-menu {
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    background-color: var(--surface-color);
    border: 1px solid  var(--border-color);
    transition: all 0.2s ease;
}

.action-menu:hover {
    background-color: var(--gray-100);
    transform: rotate(90deg);
}

/* 添加产品卡片样式 */
.add-product-card {
    border: 2px dashed  var(--border-color);
    background-color: var(--gray-50);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 100%;
    width: 100%;
    z-index: 10;
}

.add-product-card:hover {
    border-color: var(--primary-color);
    background-color: var(--info-bg);
    transform: translateY(-4px);
}

.add-product-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
}

.add-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: var(--primary-pale);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 24px;
    transition: all 0.3s ease;
}

.add-product-card:hover .add-icon {
    background-color: var(--primary-color);
    color: var(--text-color);
    transform: scale(1.1);
}

.add-text {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

.add-product-card:hover .add-text {
    color: var(--primary-color);
}

.filter-tag {
    display: flex;
    align-items: center;
    padding: 8px 14px;
    background-color: var(--primary-pale);
    border-radius: 10px;
    font-size: 13px;
    font-weight: 500;
    color: var(--primary-color);
    cursor: pointer;
    transition: all 0.25s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    border: 1px solid var(--primary-alpha-10);
}

.filter-tag:hover {
    background-color: var(--primary-light);
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0,0,0,0.1);
}

.filter-tag i {
    margin-right: 6px;
    font-size: 15px;
}

.filter-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-dropdown {
    position: relative;
}

.filter-dropdown-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 14px;
    background-color: var(--gray-50);
    border: 1px solid  var(--border-color);
    border-radius: 10px;
    font-size: 13px;
    color: var(--primary-color);
    cursor: pointer;
    transition: all 0.25s ease;
    font-weight: 500;
    box-shadow: 0 1px 3px rgba(0,0,0,0.03);
}

.filter-dropdown-btn i {
    transition: transform 0.2s ease;
}

.filter-dropdown-btn.active {
    border-color: #bbdefb;
    background-color: var(--primary-pale);
    color: var(--primary-hover);
    box-shadow: 0 2px 5px var(--primary-alpha-10);
}

.filter-dropdown-btn:hover {
    background-color: var(--primary-pale);
    border-color: #bbdefb;
    transform: translateY(-1px);
}

.filter-dropdown-btn.active i:last-child {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    min-width: 180px;
    background-color: var(--surface-color);
    border-radius: 10px;
    box-shadow: 0 4px 16px var(--black-alpha-10);
    z-index: 100;
    display: none;
    overflow: hidden;
    border: 1px solid var(--black-alpha-05);
    animation: dropdown-fade 0.2s ease;
}

.dropdown-menu.show {
    display: block;
}

@keyframes dropdown-fade {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.dropdown-item {
    padding: 9px 16px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--text-primary);
}

.dropdown-item:hover {
    background-color: var(--hover-bg);
    color: var(--primary-color);
}

.dropdown-item.active {
    background-color: var(--primary-pale);
    color: var(--primary-hover);
    font-weight: 500;
}

.batch-mode-btn {
    padding: 8px 14px;
    border-radius: 10px;
    font-size: 13px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.25s ease;
    border: 1px solid  var(--border-color);
    background-color: var(--gray-50);
    color: var(--primary-color);
    box-shadow: 0 1px 3px rgba(0,0,0,0.03);
}

.batch-mode-btn:hover {
    background-color: var(--primary-pale);
    border-color: #bbdefb;
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0,0,0,0.08);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.filter-dropdown-btn:hover {
    background-color: var(--gray-100);
}

.filter-dropdown-btn.active i {
    transform: rotate(180deg);
}

.filter-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    margin-top: 8px;
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px var(--shadow-hover);
    min-width: 180px;
    z-index: 10;
    overflow: hidden;
    display: none;
}

.filter-dropdown-menu.show {
    display: block;
}

.filter-dropdown-item {
    padding: 10px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-dropdown-item:hover {
    background-color: var(--gray-100);
}

.filter-dropdown-item.active {
    background-color: var(--primary-pale);
    color: var(--primary-color);
    font-weight: 500;
}

/* 用户设置页面样式 */
.user-settings-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: var(--surface-color);
    z-index: 1000;
    overflow-y: auto;
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    background-color: var(--surface-color);
    z-index: 10;
}

.close-settings-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--gray-100);
    border: none;
    cursor: pointer;
}

.close-settings-btn:hover {
    background-color: var(--gray-300);
}

.settings-content {
    padding: 20px;
    max-width: 900px;
    margin: 0 auto;
}

.settings-section {
    margin-bottom: 40px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-color);
    padding-bottom: 10px;
    border-bottom: 1px solid var(--gray-200);
}

.section-description {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 20px;
    line-height: 1.5;
}

/* 主账号信息卡片 */
.account-info-card {
    background-color: var(--surface-color);
    border-radius: 8px;
    border: 1px solid  var(--border-color);
    padding: 20px;
    display: flex;
    gap: 30px;
}

.account-avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.account-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
}

.change-avatar-btn {
    background-color: transparent;
    border: 1px solid var(--border-color);
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 13px;
    cursor: pointer;
}

.change-avatar-btn:hover {
    background-color: var(--gray-100);
}

.account-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.account-field {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.account-field label {
    font-size: 13px;
    color: var(--text-secondary);
}

.account-input {
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid  var(--border-color);
    font-size: 14px;
    width: 100%;
    max-width: 300px;
}

.account-value {
    font-size: 14px;
    padding: 8px 0;
}

.account-note {
    font-size: 12px;
    color: var(--gray-500);
    margin-top: 4px;
}

.account-status {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 13px;
    background-color: var(--gray-100);
    color: var(--text-secondary);
}

.account-status.active {
    background-color: var(--success-pale);
    color: var(--success-dark);
}

/* 身份验证部分 */
.auth-card {
    background-color: var(--surface-color);
    border-radius: 8px;
    border: 1px solid  var(--border-color);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 20px;
}

.auth-provider {
    display: flex;
    align-items: center;
    padding: 20px;
    border-radius: 8px;
    background-color: var(--surface-color);
    border: 1px solid  var(--border-color);
    transition: all 0.2s ease;
}

.auth-provider:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.08);
}

.provider-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
}

.google-auth .provider-icon {
    background-color: var(--surface-color);
    color: #4285F4;
    box-shadow: 0 2px 6px rgba(66, 133, 244, 0.2);
}

.phone-auth .provider-icon {
    background-color: var(--surface-color);
    color: #4CAF50;
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.2);
}

.provider-info {
    flex: 1;
}

.provider-name {
    font-weight: 500;
    margin-bottom: 4px;
}

.provider-email, .provider-phone {
    font-size: 13px;
    color: var(--text-secondary);
}

.provider-status {
    display: flex;
    align-items: center;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 13px;
    margin-right: 15px;
}

.provider-status.connected {
    background-color: var(--success-pale);
    color: var(--success-dark);
}

.provider-status.connected i {
    margin-right: 6px;
}

.provider-action-btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 13px;
    background-color: var(--surface-color);
    border: 1px solid  var(--border-color);
    cursor: pointer;
    transition: all 0.2s ease;
}

.disconnect-btn {
    color: #e53935;
}

.disconnect-btn:hover {
    background-color: var(--error-pale);
    border-color: #ffcdd2;
}

.edit-phone-btn {
    color: var(--primary-color);
}

.edit-phone-btn:hover {
    background-color: var(--primary-pale);
    border-color: #bbdefb;
}

.add-auth-provider {
    display: flex;
    justify-content: center;
    padding: 16px;
}

.add-auth-btn {
    padding: 10px 20px;
    border-radius: 8px;
    background-color: var(--gray-100);
    border: 1px dashed var(--gray-400);
    color: var(--primary-color);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.add-auth-btn:hover {
    background-color: var(--primary-pale);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.08);
}

/* 添加登录方式弹窗 */
.auth-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1100;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-modal-content {
    width: 90%;
    max-width: 500px;
    background-color: var(--surface-color);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    overflow: hidden;
    animation: modal-appear 0.3s ease;
}

@keyframes modal-appear {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-modal-header {
    padding: 20px;
    border-bottom: 1px solid  var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--surface-color);
}

.auth-modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: var(--text-color);
}

.close-auth-modal {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--surface-color);
    border: 1px solid  var(--border-color);
    cursor: pointer;
    font-size: 18px;
    transition: all 0.2s ease;
}

.close-auth-modal:hover {
    background-color: var(--gray-100);
    transform: rotate(90deg);
}

.auth-modal-body {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.auth-option {
    display: flex;
    align-items: center;
    padding: 16px;
    border-radius: 8px;
    background-color: var(--gray-50);
    border: 1px solid  var(--border-color);
    transition: all 0.2s ease;
}

.auth-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.08);
}

.auth-option-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    font-size: 20px;
    background-color: var(--surface-color);
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.auth-option-icon .ri-smartphone-line {
    color: #4CAF50;
}

.auth-option-icon .ri-google-fill {
    color: #4285F4;
}

.auth-option-info {
    flex: 1;
}

.auth-option-title {
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--text-color);
}

.auth-option-desc {
    font-size: 13px;
    color: var(--text-secondary);
}

.auth-option-btn {
    padding: 8px 16px;
    border-radius: 6px;
    background-color: var(--primary-color);
    border: none;
    color: var(--text-color);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.auth-option-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--primary-alpha-20);
}

/* 邮箱发件通道管理 */

.connected-accounts {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

/* 未配置邮箱状态 */
.no-email-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    background-color: var(--gray-50);
    border-radius: 12px;
    border: 1px dashed #bbdefb;
    margin-bottom: 20px;
    text-align: center;
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: var(--primary-pale);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.empty-state-icon i {
    font-size: 36px;
    color: var(--primary-color);
}

.no-email-state h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-color);
}

.no-email-state p {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 20px;
    max-width: 400px;
}

.add-first-email-btn {
    padding: 12px 24px;
    border-radius: 8px;
    background-color: var(--primary-color);
    color: var(--text-inverse);
    border: none;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px var(--primary-alpha-30);
}

.add-first-email-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(25, 118, 210, 0.4);
}

.account-card {
    background-color: var(--surface-color);
    border-radius: 8px;
    border: 1px solid  var(--border-color);
    overflow: hidden;
    transition: all 0.2s ease;
}

.account-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.05);
    transform: translateY(-2px);
}

.account-service {
    display: flex;
    align-items: center;
    padding: 20px;
}

.service-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
}

.gmail .service-icon {
    background-color: var(--gray-100);
    color: #ea4335;
}

.default-mail .service-icon {
    background-color: var(--success-pale);
    color: var(--success-dark);
}

.smtp .service-icon {
    background-color: var(--warning-bg, var(--text-inverse)8e1);
    color: #ff8f00;
}

.service-info {
    flex: 1;
}

.service-name {
    font-weight: 500;
    margin-bottom: 4px;
}

.service-email {
    font-size: 13px;
    color: var(--text-secondary);
}

.service-status {
    display: flex;
    align-items: center;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 13px;
    margin-right: 15px;
    background-color: var(--success-pale);
    color: var(--success-dark);
}

.service-status.primary {
    background-color: var(--primary-pale);
    color: var(--primary-hover);
}

.service-permissions {
    display: flex;
    gap: 8px;
    margin-right: 15px;
    flex-wrap: wrap;
}

.permission-tag {
    background-color: var(--gray-100);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: var(--text-secondary);
}

.service-actions {
    display: flex;
    gap: 8px;
}

.service-action-btn {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--surface-color);
    border: 1px solid  var(--border-color);
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s ease;
}

.service-action-btn:hover {
    background-color: var(--gray-100);
    color: var(--primary-color);
}

.set-primary-btn {
    color: #f57c00;
}

.set-primary-btn:hover {
    background-color: var(--warning-bg, var(--text-inverse)3e0);
    color: #e65100;
    transform: scale(1.1);
}

.add-account-section {
    margin-top: 20px;
}

.add-email-btn, .add-account-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 4px;
    background-color: var(--surface-color);
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    font-weight: 500;
    cursor: pointer;
    margin-bottom: 10px;
    transition: all 0.2s ease;
}

.add-email-btn:hover, .add-account-btn:hover {
    background-color: var(--hover-bg);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--primary-alpha-10);
}

.account-note {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    font-size: 13px;
    color: var(--text-secondary);
    line-height: 1.5;
    padding: 10px;
    background-color: var(--gray-50);
    border-radius: 4px;
}

.account-note i {
    color: var(--primary-color);
    margin-top: 2px;
}

/* 添加邮箱弹窗 */
.email-modal, .smtp-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1100;
    display: flex;
    align-items: center;
    justify-content: center;
}

.email-modal-content, .smtp-modal-content {
    width: 90%;
    max-width: 500px;
    background-color: var(--surface-color);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    overflow: hidden;
    animation: modal-appear 0.3s ease;
}

.email-modal-header, .smtp-modal-header {
    padding: 20px;
    border-bottom: 1px solid  var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--gray-50);
}

.email-modal-header h3, .smtp-modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: var(--text-color);
}

.close-email-modal, .close-smtp-modal {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--surface-color);
    border: 1px solid  var(--border-color);
    cursor: pointer;
    font-size: 18px;
    transition: all 0.2s ease;
}

.close-email-modal:hover, .close-smtp-modal:hover {
    background-color: var(--gray-100);
    transform: rotate(90deg);
}

.email-modal-body, .smtp-modal-body {
    padding: 20px;
}

/* 邮箱类型选择 */
.email-type-options {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.email-type-option {
    display: flex;
    align-items: center;
    padding: 16px;
    border-radius: 12px;
    background-color: var(--surface-color);
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.email-type-option:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: var(--primary-light);
}

.email-type-option:active {
    transform: translateY(-1px);
}

.email-type-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    background-color: var(--gray-50);
    margin-right: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.email-type-info {
    flex: 1;
}

.email-type-title {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 4px;
    color: var(--text-color);
}

.email-type-desc {
    font-size: 13px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.email-type-btn {
    padding: 10px 20px;
    border-radius: 8px;
    background-color: var(--primary-color);
    border: none;
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.email-type-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--primary-alpha-30);
}

/* 邮箱配置弹窗 */
.email-config-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.email-config-modal-content {
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    background-color: var(--surface-color);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    overflow: hidden;
    animation: modal-appear 0.3s ease;
}

.email-config-modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--gray-50);
}

.email-config-modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: var(--text-color);
    display: flex;
    align-items: center;
}

.close-email-config-modal {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    cursor: pointer;
    font-size: 18px;
    transition: all 0.2s ease;
}

.close-email-config-modal:hover {
    background-color: var(--gray-100);
    transform: rotate(90deg);
}

.email-config-modal-body {
    padding: 24px;
    max-height: calc(90vh - 80px);
    overflow-y: auto;
}

/* 配置指导卡片 */
.config-guide-card {
    background-color: var(--primary-alpha-5);
    border: 1px solid var(--primary-alpha-20);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 24px;
}

.config-guide-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-weight: 600;
    color: var(--primary-color);
}

.config-guide-header i {
    margin-right: 8px;
    font-size: 18px;
}

.config-guide-content ol {
    margin: 0 0 16px 0;
    padding-left: 20px;
    color: var(--text-color);
}

.config-guide-content li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.guide-link {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.guide-link:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--primary-alpha-30);
}

.guide-link i {
    margin-right: 6px;
}

/* 邮箱配置表单 */
.email-config-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.auto-config-section {
    background-color: var(--gray-50);
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
}

.auto-config-section h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

.auto-config-section input[readonly] {
    background-color: var(--background-color);
    color: var(--text-light);
    cursor: not-allowed;
}

.test-connection-btn, .save-config-btn {
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
}

.test-connection-btn {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

.test-connection-btn:hover {
    background-color: var(--gray-50);
    transform: translateY(-1px);
}

.save-config-btn {
    background-color: var(--primary-color);
    color: white;
}

.save-config-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--primary-alpha-30);
}

/* Gmail OAuth样式 */
.gmail-oauth-section {
    text-align: center;
}

.oauth-info-card {
    background-color: var(--surface-color);
    border: 2px solid var(--border-color);
    border-radius: 16px;
    padding: 32px 24px;
    margin-bottom: 24px;
}

.oauth-icon {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background-color: var(--primary-alpha-10);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 28px;
    color: var(--primary-color);
}

.oauth-content h4 {
    margin: 0 0 12px 0;
    font-size: 18px;
    color: var(--text-color);
}

.oauth-content p {
    margin: 0 0 24px 0;
    color: var(--text-secondary);
    line-height: 1.5;
}

.oauth-authorize-btn {
    display: inline-flex;
    align-items: center;
    padding: 12px 24px;
    background-color: #4285F4;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 20px;
}

.oauth-authorize-btn:hover {
    background-color: #3367D6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
}

.oauth-authorize-btn i {
    margin-right: 8px;
    font-size: 18px;
}

.oauth-permissions {
    text-align: left;
    background-color: var(--gray-50);
    border-radius: 8px;
    padding: 16px;
    margin-top: 16px;
}

.oauth-permissions p {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
}

.oauth-permissions ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.oauth-permissions li {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    font-size: 14px;
    color: var(--text-secondary);
}

.oauth-permissions li i {
    margin-right: 8px;
    color: var(--success-color);
    font-size: 16px;
}

/* 已授权账号列表 */
.authorized-accounts {
    margin-top: 24px;
    text-align: left;
}

.authorized-accounts h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: var(--text-color);
}

.authorized-accounts-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.authorized-account-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: var(--gray-50);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.account-info {
    display: flex;
    align-items: center;
}

.account-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 12px;
    background-color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.account-details h5 {
    margin: 0 0 2px 0;
    font-size: 14px;
    color: var(--text-color);
}

.account-details p {
    margin: 0;
    font-size: 12px;
    color: var(--text-secondary);
}

/* 其他邮箱配置样式 */
.email-template-section {
    background-color: var(--gray-50);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.email-template-section .form-group {
    margin-bottom: 0;
}

.smtp-config-section, .imap-config-section {
    margin-bottom: 20px;
}

.smtp-config-section h4, .imap-config-section h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: var(--text-color);
    font-weight: 600;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

.basic-config-section {
    margin-bottom: 20px;
}

.basic-config-section h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    color: var(--text-color);
    font-weight: 600;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

/* 高级配置展开/折叠 */
.advanced-config-section {
    margin-bottom: 20px;
}

.advanced-config-toggle {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: var(--gray-50);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.advanced-config-toggle:hover {
    background-color: var(--gray-100);
}

.advanced-config-toggle i {
    margin-right: 8px;
    transition: transform 0.2s ease;
    color: var(--text-secondary);
}

.advanced-config-toggle.expanded i {
    transform: rotate(90deg);
}

.advanced-config-toggle span {
    font-weight: 500;
    color: var(--text-color);
}

.advanced-config-content {
    margin-top: 16px;
    padding: 16px;
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 统一邮箱配置弹窗样式 */
.email-config-modal-unified {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.email-config-modal-content-unified {
    width: 90%;
    max-width: 700px;
    max-height: 90vh;
    background-color: var(--surface-color);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    overflow: hidden;
    animation: modal-appear 0.3s ease;
    display: flex;
    flex-direction: column;
}

.email-config-modal-header-unified {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--gray-50);
    flex-shrink: 0;
}

.email-config-modal-header-unified h3 {
    margin: 0;
    font-size: 18px;
    color: var(--text-color);
}

.close-email-config-modal-unified {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    cursor: pointer;
    font-size: 18px;
    transition: all 0.2s ease;
}

.close-email-config-modal-unified:hover {
    background-color: var(--gray-100);
    transform: rotate(90deg);
}

/* 邮箱类型选择器 */
.email-type-selector {
    background-color: var(--gray-50);
    border-bottom: 1px solid var(--border-color);
    padding: 20px 24px;
    flex-shrink: 0;
}

.email-type-dropdown {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--surface-color);
    color: var(--text-color);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.email-type-dropdown:hover {
    border-color: var(--primary-color);
}

.email-type-dropdown:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-alpha-20);
}

/* 配置区域内容 */
.email-config-modal-body-unified {
    padding: 24px;
    max-height: calc(90vh - 200px);
    overflow-y: auto;
    flex: 1;
}

.email-config-section {
    display: none;
    animation: fadeIn 0.3s ease;
}

.email-config-section.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计优化 */
@media (max-width: 768px) {
    .email-config-modal-content-unified {
        width: 95%;
        max-height: 95vh;
    }

    .email-config-modal-body-unified {
        padding: 16px;
        max-height: calc(95vh - 200px);
    }

    .email-type-selector {
        padding: 16px;
    }

    .email-type-dropdown {
        padding: 10px 12px;
        font-size: 13px;
    }

    .oauth-info-card {
        padding: 24px 16px;
    }

    .email-type-option {
        padding: 12px;
    }

    .email-type-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
        margin-right: 12px;
    }

    .email-type-title {
        font-size: 15px;
    }

    .email-type-desc {
        font-size: 12px;
    }

    .email-type-btn {
        padding: 8px 16px;
        font-size: 13px;
    }
}

.email-option-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    font-size: 20px;
    background-color: var(--surface-color);
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.email-option-icon .ri-google-fill {
    color: #4285F4;
}

.email-option-icon .ri-mail-settings-line {
    color: #ff8f00;
}

.email-option-info {
    flex: 1;
}

.email-option-title {
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--text-color);
}

.email-option-desc {
    font-size: 13px;
    color: var(--text-secondary);
}

.email-option-btn {
    padding: 8px 16px;
    border-radius: 6px;
    background-color: var(--primary-color);
    border: none;
    color: var(--text-color);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.email-option-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--primary-alpha-20);
}

/* SMTP配置表单 */
.smtp-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-group label {
    font-size: 13px;
    color: var(--text-secondary);
    font-weight: 500;
}

.form-group input, .form-group select {
    padding: 10px 12px;
    border-radius: 4px;
    border: 1px solid  var(--border-color);
    font-size: 14px;
    transition: all 0.2s ease;
}

.form-group input:focus, .form-group select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px var(--primary-alpha-10);
}

.form-row {
    display: flex;
    gap: 16px;
}

.form-group.half {
    flex: 1;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 8px;
}

.test-smtp-btn {
    padding: 10px 16px;
    border-radius: 4px;
    background-color: var(--surface-color);
    border: 1px solid  var(--border-color);
    color: var(--text-color);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.test-smtp-btn:hover {
    background-color: var(--gray-100);
}

.save-smtp-btn {
    padding: 10px 16px;
    border-radius: 4px;
    background-color: var(--primary-color);
    border: none;
    color: var(--text-inverse);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.save-smtp-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--primary-alpha-20);
}

/* 邮箱统计信息 */
.email-stats-section {
    margin-top: 30px;
    background-color: var(--surface-color);
    border-radius: 8px;
    border: 1px solid  var(--border-color);
    padding: 20px;
}

.stats-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-color);
}

.email-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
}

.stat-card {
    background-color: var(--gray-50);
    border-radius: 8px;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: all 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.05);
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--surface-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: var(--primary-color);
    box-shadow: 0 2px 6px var(--primary-alpha-10);
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 13px;
    color: var(--text-secondary);
}

/* 安全设置 */
.security-options {
    background-color: var(--surface-color);
    border-radius: 8px;
    border: 1px solid  var(--border-color);
    overflow: hidden;
}

.security-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-light);
}

.security-option:last-child {
    border-bottom: none;
}

.option-info {
    flex: 1;
}

.option-name {
    font-weight: 500;
    margin-bottom: 4px;
}

.option-description {
    font-size: 13px;
    color: var(--text-secondary);
}

.option-btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 13px;
    background-color: var(--surface-color);
    border: 1px solid  var(--border-color);
    cursor: pointer;
}

.option-btn:hover {
    background-color: var(--gray-100);
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--gray-400);
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: var(--surface-color);
    transition: .4s;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:focus + .slider {
    box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 24px;
}

.slider.round:before {
    border-radius: 50%;
}

/* 登录页面样式 */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: var(--gray-100);
}

.login-card {
    width: 400px;
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 2px 10px var(--black-alpha-10);
    padding: 30px;
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-logo {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
}

.login-logo h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
}

.login-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
}

.login-methods {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.google-login-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: var(--surface-color);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.google-login-btn:hover {
    background-color: #f8f8f8;
}

.google-login-btn i {
    color: var(--google-red);
    font-size: 18px;
}

.login-divider {
    display: flex;
    align-items: center;
    text-align: center;
    margin: 15px 0;
}

.login-divider::before,
.login-divider::after {
    content: "";
    flex: 1;
    border-bottom: 1px solid #ddd;
}

.login-divider span {
    padding: 0 10px;
    font-size: 14px;
    color: var(--text-secondary);
}

.phone-login-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.login-input-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.login-input-group label {
    font-size: 13px;
    color: var(--text-secondary);
}

.login-input-group input {
    padding: 10px 12px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    font-size: 14px;
}

.verification-code-group {
    margin-top: 5px;
}

.verification-code-container {
    display: flex;
    gap: 10px;
}

.verification-code-container input {
    flex: 1;
}

.send-code-btn {
    padding: 10px 12px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: var(--gray-100);
    color: var(--text-inverse);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    white-space: nowrap;
}

.send-code-btn:hover {
    background-color: var(--gray-300);
}

.send-code-btn.disabled {
    background-color: var(--gray-100);
    color: var(--text-tertiary);
    cursor: not-allowed;
}

.phone-login-btn {
    padding: 12px;
    border-radius: 4px;
    border: none;
    background-color: var(--primary-color);
    color: var(--text-color);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.phone-login-btn:hover {
    background-color: var(--primary-dark);
}

.login-footer {
    margin-top: 30px;
    text-align: center;
    font-size: 14px;
    color: var(--text-secondary);
}

.login-footer a {
    color: var(--primary-color);
    text-decoration: none;
}

.login-footer a:hover {
    text-decoration: underline;
}

/* Google认证页面样式 */
.google-auth-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: var(--gray-100);
}

.google-auth-card {
    width: 450px;
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 2px 10px var(--black-alpha-10);
    overflow: hidden;
}

.google-auth-header {
    padding: 24px;
    text-align: center;
    border-bottom: 1px solid #f1f1f1;
}

.google-logo {
    display: inline-block;
}

.google-auth-content {
    padding: 24px 40px 36px;
}

.google-auth-content h2 {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 10px;
    color: #202124;
}

.google-auth-content p {
    font-size: 16px;
    color: #5f6368;
    margin-bottom: 30px;
}

.google-account-select {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 30px;
}

.google-account-option {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid transparent;
    cursor: pointer;
}

.google-account-option:hover {
    background-color: var(--background-color);
}

.google-account-option.selected {
    border-color: #dadce0;
    background-color: var(--background-color);
}

.account-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 15px;
}

.account-info {
    flex: 1;
}

.account-name {
    font-size: 14px;
    font-weight: 500;
    color: #202124;
}

.account-email {
    font-size: 14px;
    color: #5f6368;
}

.account-check {
    color: var(--google-blue);
}

.add-account-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f1f3f4;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: #5f6368;
}

.google-auth-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
}

.google-cancel-btn,
.google-next-btn {
    padding: 10px 24px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
}

.google-cancel-btn {
    background-color: transparent;
    color: var(--google-blue);
    border: none;
}

.google-next-btn {
    background-color: var(--google-blue);
    color: var(--text-color);
    border: none;
}

.google-auth-footer {
    padding: 16px 40px;
    border-top: 1px solid #f1f1f1;
}

.auth-footer-links {
    display: flex;
    gap: 24px;
    font-size: 12px;
}

.auth-footer-links a {
    color: #5f6368;
    text-decoration: none;
}

.auth-footer-links a:hover {
    text-decoration: underline;
}

/* 授权请求页面样式 */
.auth-request-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: var(--gray-100);
}

.auth-request-card {
    width: 500px;
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 2px 10px var(--black-alpha-10);
    overflow: hidden;
}

.auth-request-header {
    padding: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #f1f1f1;
    position: relative;
}

.app-logo {
    display: flex;
    align-items: center;
}

.app-logo h2 {
    font-size: 18px;
    font-weight: 500;
}

.auth-divider {
    width: 30px;
    height: 1px;
    background-color: #dadce0;
    margin: 0 15px;
}

.google-logo-small {
    display: inline-block;
}

.auth-request-content {
    padding: 24px 40px 36px;
}

.auth-request-content h2 {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 20px;
    color: #202124;
}

.auth-user-info {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    padding: 10px;
    background-color: var(--background-color);
    border-radius: 4px;
}

.auth-user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 10px;
}

.auth-permissions {
    margin-bottom: 30px;
}

.auth-permissions h3 {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 15px;
    color: #202124;
}

.permission-list {
    list-style: none;
}

.permission-list li {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-size: 14px;
    color: #5f6368;
}

.permission-list li i {
    margin-right: 10px;
    color: var(--google-blue);
}

.auth-note {
    font-size: 13px;
    color: #5f6368;
    margin-bottom: 30px;
    line-height: 1.5;
}

.auth-note a {
    color: var(--google-blue);
    text-decoration: none;
}

.auth-note a:hover {
    text-decoration: underline;
}

.auth-request-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.auth-cancel-btn,
.auth-allow-btn {
    padding: 10px 24px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
}

.auth-cancel-btn {
    background-color: transparent;
    color: var(--google-blue);
    border: none;
}

.auth-allow-btn {
    background-color: var(--google-blue);
    color: var(--text-color);
    border: none;
}

/* 移动设备适配 */
@media (max-width: 768px) {
    .sidebar {
        width: 60px;
    }

    .sidebar-logo h1,
    .menu-item-content span,
    .menu-section h2,
    .user-profile .user-info,
    .submenu-arrow {
        display: none;
    }

    /* 移动设备上子菜单样式 */
    .menu-item.has-submenu.expanded .submenu {
        position: absolute;
        left: 60px;
        top: 0;
        background-color: var(--surface-color);
        box-shadow: 4px 0 8px rgba(0,0,0,0.1);
        border-radius: 0 6px 6px 0;
        padding: 8px;
        z-index: 100;
        width: 180px;
    }

    .submenu-item span {
        display: inline-block !important;
    }

    .main-content {
        margin-left: 60px;
        width: calc(100vw - 60px); /* 修正移动端宽度计算 */
        min-width: 0; /* 允许收缩 */
        overflow-x: hidden; /* 防止水平滚动 */
    }

    /* 建联记录容器移动端优化 */
    .outreach-container {
        padding: 16px 12px; /* 减少内边距 */
        overflow-x: hidden; /* 防止水平溢出 */
        width: 100%;
        box-sizing: border-box;
    }

    /* 产品库容器移动端优化 */
    .content-area {
        padding: 16px 12px; /* 减少内边距 */
        overflow-x: hidden; /* 防止水平溢出 */
        width: 100%;
        box-sizing: border-box;
    }

    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .account-info-card {
        flex-direction: column;
    }

    .account-avatar-section {
        align-items: center;
        margin-bottom: 20px;
    }

    .login-card,
    .google-auth-card,
    .auth-request-card {
        width: 90%;
        max-width: 400px;
    }

    .google-auth-content,
    .auth-request-content {
        padding: 20px;
    }
}

/* 更小屏幕适配 (手机竖屏) */
@media (max-width: 480px) {
    .main-content {
        margin-left: 60px;
        width: calc(100vw - 60px);
        min-width: 0;
        overflow-x: hidden;
    }

    /* 建联记录和产品库进一步优化 */
    .outreach-container,
    .content-area {
        padding: 12px 8px;
        overflow-x: hidden;
        width: 100%;
        box-sizing: border-box;
    }

    /* 建联记录头部进一步压缩 */
    .outreach-header {
        padding: 8px 12px;
        margin-bottom: 12px;
    }

    .page-title {
        font-size: 16px;
    }

    /* 搜索过滤器优化 */
    .search-filter {
        gap: 8px;
    }

    .search-input {
        font-size: 14px;
        padding: 6px 12px;
    }

    /* 建联记录项进一步优化 */
    .outreach-item {
        padding: 12px;
        gap: 8px;
        margin-bottom: 8px;
        min-height: 60px;
    }

    .outreach-creator {
        min-width: 160px;
    }

    .creator-avatar {
        width: 36px !important;
        height: 36px !important;
    }

    .creator-name {
        font-size: 13px;
    }

    .creator-stats span {
        font-size: 11px;
        padding: 2px 6px;
    }

    .insight-item {
        font-size: 10px;
        padding: 2px 6px;
    }

    .outreach-product {
        min-width: 120px;
        padding: 0 8px;
    }

    .outreach-intent {
        min-width: 140px;
        padding: 0 8px;
    }

    .outreach-status {
        min-width: 80px;
        padding: 0 6px;
    }

    .outreach-date {
        min-width: 80px;
        padding: 0 6px;
    }

    .outreach-actions {
        min-width: 60px;
        padding-left: 4px;
    }

    .action-btn {
        height: 24px;
        padding: 0 6px;
        font-size: 10px;
        gap: 2px;
    }

    .detail-btn {
        background: var(--primary-color);
        color: var(--surface-color);
    }

    /* 产品网格进一步优化 */
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 10px;
    }

    /* 状态标签优化 */
    .status-tabs {
        padding: 2px;
        margin-bottom: 12px;
    }

    .status-tab {
        padding: 6px 8px;
        font-size: 12px;
    }
}

/* 步骤显示隐藏管理 */
.step {
    display: none;
}

.step.active {
    display: block;
}

/* AI助手页面样式 */
.ai-assistant-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - var(--header-height));
    background-color: var(--background-color);
    position: relative;
    flex: 1;
    overflow: hidden; /* 防止双重滚动 */
}

/* AI头部样式已移除 - 不再需要蓝色条块 */

.ai-avatar {
    width: 42px;
    height: 42px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-color), #64b5f6);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 14px;
    color: var(--text-color);
    box-shadow: 0 2px 6px var(--primary-alpha-20);
}

.ai-avatar i {
    font-size: 20px;
}

/* 新建商品分析会话样式 */
.new-product-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    padding: 20px;
    flex: 1;
    overflow-y: auto;
}

.welcome-message {
    max-width: 700px;
    text-align: center;
    margin-bottom: 24px; /* 减少底部间距 */
    background-color: var(--surface-color);
    border-radius: 16px;
    padding: 20px; /* 减少内边距 */
    box-shadow: 0 4px 12px var(--shadow-color);
}

.welcome-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), #64b5f6);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: var(--text-color);
    box-shadow: 0 4px 12px var(--primary-alpha-20);
}

.welcome-icon i {
    font-size: 40px;
}

.welcome-message h2 {
    font-size: 24px;
    margin-bottom: 16px;
    color: var(--text-color);
}

.welcome-message p {
    font-size: 16px;
    color: var(--text-light);
    margin-bottom: 16px;
    line-height: 1.5;
}

.welcome-message ul {
    text-align: left;
    margin: 20px auto;
    max-width: 500px;
    padding-left: 20px;
}

.welcome-message ul li {
    margin-bottom: 10px;
    color: var(--text-light);
    line-height: 1.5;
}

.welcome-hint {
    font-size: 14px;
    color: var(--text-light);
    font-style: italic;
}

.central-input-container {
    width: 100%;
    max-width: 700px;
    position: relative;
    margin-bottom: 20px; /* 减少底部间距 */
}

.central-input {
    width: 100%;
    height: 60px;
    border-radius: 12px;
    border: 1px solid  var(--border-color);
    padding: 16px 60px 16px 20px;
    font-size: 16px;
    resize: none;
    box-shadow: 0 4px 12px var(--shadow-color);
    transition: all 0.3s ease;
}

.central-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px var(--primary-alpha-10);
}

.central-send-button {
    position: absolute;
    right: 8px;
    bottom: 8px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    z-index: 20;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.central-send-button:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
}

.central-send-button:active {
    transform: scale(0.95);
}

.central-send-button i {
    font-size: 20px;
}

.quick-prompts {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 12px;
    max-width: 700px;
}

.quick-prompt-btn {
    padding: 10px 16px;
    border-radius: 8px;
    background-color: var(--surface-color);
    border: 1px solid  var(--border-color);
    color: var(--text-light);
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px var(--black-alpha-05);
}

.quick-prompt-btn:hover {
    background-color: var(--hover-bg);
    border-color: var(--border-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--black-alpha-10);
}

.quick-prompt-btn i {
    margin-right: 8px;
    font-size: 16px;
    color: var(--primary-color);
}

/* 分析步骤样式 */
.analysis-steps {
    background-color: var(--background-color);
    border-radius: 12px;
    padding: 16px;
    margin: 10px auto; /* 居中对齐 */
    max-width: 700px; /* 调整最大宽度 */
    width: 100%; /* 统一宽度 */
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.step-item {
    margin-bottom: 16px;
    border-left: 2px solid  var(--border-color);
    padding-left: 16px;
    position: relative;
    animation: fadeInUp 0.5s ease forwards;
    opacity: 0;
}

.step-item:nth-child(1) {
    animation-delay: 0.1s;
}

.step-item:nth-child(2) {
    animation-delay: 0.3s;
}

.step-item:nth-child(3) {
    animation-delay: 0.5s;
}

.step-item:last-child {
    margin-bottom: 0;
}

.step-item:before {
    content: '';
    position: absolute;
    left: -6px;
    top: 0;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--primary-color);
    animation: pulse 2s infinite;
}

.step-label {
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--primary-color);
    display: flex;
    align-items: center;
}

.step-label i {
    margin-right: 8px;
    font-size: 16px;
}

.step-content {
    color: var(--text-secondary);
    font-size: 14px;
    line-height: 1.5;
}

.step-content ul {
    margin: 8px 0;
    padding-left: 20px;
}

.step-content ul li {
    margin-bottom: 4px;
}

/* 快速操作按钮样式 */
.quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.quick-action-btn {
    padding: 10px 16px;
    background-color: var(--gray-100);
    border: 1px solid  var(--border-color);
    border-radius: 8px;
    color: var(--text-color);
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
}

.quick-action-btn:hover {
    background-color: #e8f4fd;
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 6px var(--black-alpha-10);
}

.quick-action-btn i {
    margin-right: 8px;
    font-size: 16px;
}

.ai-info {
    flex: 1;
}

.ai-name {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 3px;
    color: var(--text-color);
}

/* 已移除 .ai-product 相关样式 */

/* 移除AI助手右上角的聊天和建联记录按钮 */

.chat-container {
    position: absolute;
    top: 0; /* 移除AI头部高度偏移 */
    bottom: 100px; /* 增加底部空间，确保内容完全显示 */
    left: 0;
    right: 0;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 24px;
    padding-bottom: 12px; /* 减小底部内边距 */
    display: flex;
    flex-direction: column;
    gap: 24px;
    background-color: var(--background-color);
    background-image: radial-gradient(circle at 25px 25px, var(--primary-alpha-05) 2px, transparent 0),
                      radial-gradient(circle at 75px 75px, rgba(25, 118, 210, 0.03) 2px, transparent 0);
    background-size: 100px 100px;
    /* Chrome滚动优化 */
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    /* 强制建立新的层叠上下文 */
    transform: translateZ(0);
    /* 确保在Chrome中正确计算高度 */
    contain: layout style paint;
}

/* Chrome滚动条样式 */
.chat-container::-webkit-scrollbar {
    width: 8px;
}

.chat-container::-webkit-scrollbar-track {
    background: var(--background-color);
    border-radius: 4px;
}

.chat-container::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.chat-container::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

.message {
    display: flex;
    max-width: 85%;
    position: relative;
}

/* 商品分析卡片消息容器特殊处理 */
.message.wide-message {
    max-width: 95%;
}

/* 为包含分析卡片的消息提供更宽的显示空间 */
.ai-message:has(.product-analysis-card),
.ai-message:has(.analysis-steps),
.ai-message:has(.creators-recommendation-container),
.ai-message:has(.email-generation-container) {
    max-width: 95%;
}

.message-avatar {
    width: 38px;
    height: 38px;
    border-radius: 12px;
    margin-right: 14px;
    flex-shrink: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.message-content {
    background-color: var(--surface-color);
    padding: 16px 20px;
    border-radius: 16px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
    position: relative;
    line-height: 1.5;
    font-size: 15px;
}

.message-content p {
    margin-bottom: 12px;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content ul {
    margin-top: 8px;
    margin-bottom: 12px;
}

.message-content ul li {
    margin-bottom: 6px;
}

.message-time {
    font-size: 12px;
    color: var(--text-light);
    margin-top: 6px;
    margin-left: 4px;
}

.user-message {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    margin-right: 0;
    margin-left: 14px;
}

.user-message .message-content {
    background: linear-gradient(135deg, var(--primary-color), #2196f3);
    color: var(--text-inverse);
    border-bottom-right-radius: 4px;
}

.ai-message {
    align-self: flex-start;
}

.ai-message .message-content {
    background-color: var(--surface-color);
    border-bottom-left-radius: 4px;
}

.ai-message .message-avatar {
    background: linear-gradient(135deg, var(--primary-color), #64b5f6);
    color: var(--text-inverse);
    display: flex;
    align-items: center;
    justify-content: center;
}

.ai-message .message-avatar i {
    font-size: 20px;
}

.ai-results {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 16px;
    margin-bottom: 8px;
}

.creator-card {
    background-color: var(--surface-color);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 3px 10px var(--black-alpha-08);
    display: flex;
    margin-bottom: 16px;
    transition: all 0.3s ease;
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.creator-card:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary-color), #64b5f6);
}

.creator-card:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
    transform: translateY(-3px);
}

.creator-avatar {
    width: 70px;
    height: 70px;
    border-radius: 14px;
    margin-right: 20px;
    flex-shrink: 0;
    object-fit: cover;
    box-shadow: 0 3px 8px rgba(0,0,0,0.1);
    border: 2px solid var(--surface-color);
}

.creator-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.creator-name {
    font-weight: 600;
    font-size: 18px;
    margin-bottom: 8px;
    color: var(--text-color);
    display: flex;
    align-items: center;
}

.creator-name .verified {
    color: var(--primary-color);
    margin-left: 6px;
    font-size: 16px;
}

.creator-stats {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 6px;
}

.creator-stats span {
    display: flex;
    align-items: center;
    background-color: var(--background-color);
    padding: 2px 8px;
    border-radius: 12px;
    transition: all 0.2s ease;
}

.outreach-item:hover .creator-stats span {
    background-color: var(--surface-hover);
}

.creator-stats span i {
    margin-right: 4px;
    font-size: 14px;
    color: var(--primary-color);
}

.creator-stats span {
    display: flex;
    align-items: center;
}

.creator-stats span i {
    margin-right: 6px;
    font-size: 16px;
    color: var(--primary-color);
}

.creator-description {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 12px;
    line-height: 1.5;
}

.ai-suggestion {
    background-color: var(--surface-elevated, var(--surface-color));
    padding: 12px 15px;
    border-radius: 8px;
    font-size: 14px;
    color: var(--text-color);
    margin-bottom: 12px;
    line-height: 1.5;
    border-left: 3px solid var(--primary-color);
}

.ai-suggestion i {
    color: var(--primary-color);
    margin-right: 6px;
}

.creator-actions {
    display: flex;
    gap: 12px;
    margin-top: 12px;
}

.creator-actions button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
}

.add-outreach-btn {
    background-color: var(--primary-color);
    color: var(--text-inverse);
}

.add-outreach-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

.view-detail-btn {
    background-color: var(--gray-100);
    color: var(--text-color);
}

.view-detail-btn:hover {
    background-color: var(--gray-200);
    transform: translateY(-1px);
}

.ai-json {
    background-color: var(--background-color);
    border-radius: 6px;
    padding: 12px;
    font-family: monospace;
    font-size: 14px;
    overflow-x: auto;
    color: var(--text-color);
    white-space: pre-wrap;
    margin-top: 10px;
}

.input-area {
    padding: 18px 24px;
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    background-color: var(--surface-color);
    box-shadow: 0 -2px 10px rgba(0,0,0,0.03);
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
}

.chat-input {
    flex: 1;
    padding: 14px 20px;
    border-radius: 24px;
    border: 1px solid var(--border-color);
    background-color: var(--surface-color);
    font-size: 15px;
    resize: none;
    height: 52px;
    max-height: 120px;
    overflow-y: auto;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05) inset;
}

.chat-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: var(--surface-color);
    box-shadow: 0 0 0 3px var(--primary-alpha-10);
}

.send-button {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), #2196f3);
    color: var(--text-inverse);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px var(--primary-alpha-30);
    transition: all 0.2s ease;
}

.send-button:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 8px rgba(25, 118, 210, 0.4);
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
}

.send-button i {
    font-size: 20px;
}

.fullscreen-notice {
    background-color: var(--text-color); 
    color: var(--text-inverse);
    padding: 10px;
    text-align: center;
    border-radius: 4px;
    margin-bottom: 15px;
}

.suggestion-tag {
    display: inline-block;
    background-color: var(--primary-pale);
    color: var(--primary-hover);
    padding: 8px 14px;
    border-radius: 20px;
    margin-right: 10px;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.suggestion-tag:hover {
    background-color: var(--primary-light);
    transform: translateY(-2px);
    box-shadow: 0 2px 5px var(--shadow-color);
}

.suggestion-tag i {
    margin-right: 6px;
    font-size: 14px;
}

/* 输入区域操作按钮 */
.input-actions {
    display: flex;
    gap: 8px;
    margin-right: 12px;
}

.input-action-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--gray-50);
    border: 1px solid  var(--border-color);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.input-action-btn:hover {
    background-color: var(--primary-pale);
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* 分析步骤样式 */
.analysis-steps, .analysis-complete {
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-width: 750px; /* 调整最大宽度 */
    width: 100%; /* 统一宽度 */
}

/* 紧凑型商品分析卡片样式 */
.product-analysis-card-compact {
    background: linear-gradient(135deg, var(--surface-color) 0%, rgba(255, 255, 255, 0.95) 100%);
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    margin: 8px auto 16px auto;
    overflow: hidden;
    border: 1px solid var(--border-light);
    max-width: 900px;
    width: 100%;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-analysis-card-compact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), #10b981);
}

.product-analysis-card-compact:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.product-analysis-header-compact {
    background: linear-gradient(135deg, var(--gray-50) 0%, rgba(248, 250, 252, 0.8) 100%);
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-light);
}

.product-analysis-header-compact h4 {
    margin: 0;
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.product-analysis-header-compact h4 i {
    margin-right: 8px;
    color: var(--primary-color);
    font-size: 18px;
    background: var(--primary-alpha-10);
    padding: 6px;
    border-radius: 6px;
}

.product-analysis-content-compact {
    padding: 16px;
}

/* 网格布局 */
.product-info-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.product-info-row {
    display: flex;
    gap: 16px;
    align-items: flex-start;
}

.info-item-compact {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.info-item-compact.full-width {
    flex: 1 1 100%;
}

.info-label-compact {
    font-size: 13px;
    font-weight: 600;
    color: var(--text-secondary);
    margin: 0;
    padding: 0;
    line-height: 1.3;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.editable-field-compact {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-light);
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    background: var(--surface-color);
    transition: all 0.2s ease;
    line-height: 1.4;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 36px;
    box-sizing: border-box;
}

.editable-field-compact:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-alpha-10);
    background: white;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
}

.editable-field-compact:disabled {
    background: linear-gradient(135deg, var(--gray-50) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-color: var(--border-light);
    color: var(--text-secondary);
    cursor: not-allowed;
}

/* 操作按钮区域 */
.product-analysis-actions-compact {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 12px 16px;
    border-top: 1px solid var(--border-light);
    background: linear-gradient(135deg, var(--gray-50) 0%, rgba(248, 250, 252, 0.8) 100%);
}

.primary-btn-compact {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 13px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    box-shadow: 0 2px 8px var(--primary-alpha-20);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.primary-btn-compact::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.primary-btn-compact:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 12px var(--primary-alpha-30);
}

.primary-btn-compact:hover::before {
    left: 100%;
}

.primary-btn-compact:active {
    transform: translateY(0);
}

.edit-product-btn-compact {
    background: var(--surface-color);
    border: 1px solid var(--border-light);
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 13px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--text-primary);
    transition: all 0.2s ease;
}

.edit-product-btn-compact:hover {
    background: var(--primary-alpha-05);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 紧凑型卡片的额外优化 */
.product-analysis-card-compact .product-info-row:not(:last-child) {
    border-bottom: 1px solid rgba(0, 0, 0, 0.03);
    padding-bottom: 12px;
    margin-bottom: 12px;
}

.product-analysis-card-compact .product-info-row:last-child {
    margin-bottom: 0;
}

/* 为不同类型的字段添加视觉区分 */
.info-item-compact:nth-child(1) .info-label-compact {
    color: var(--primary-color);
}

.info-item-compact:nth-child(2) .info-label-compact {
    color: var(--secondary-color);
}

/* 全宽字段的特殊样式 */
.info-item-compact.full-width .info-label-compact {
    color: var(--text-primary);
    font-weight: 700;
}

.info-item-compact.full-width .editable-field-compact {
    border-color: var(--primary-alpha-20);
    background: linear-gradient(135deg, var(--surface-color) 0%, rgba(255, 255, 255, 0.8) 100%);
}

/* 可编辑标签容器样式 */
.editable-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    min-height: 32px;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--surface-color);
    transition: all 0.2s ease;
}

.editable-tags-container:hover {
    border-color: var(--primary-alpha-30);
}

/* 可编辑标签样式 */
.editable-tag {
    display: inline-block;
    padding: 4px 8px;
    background: linear-gradient(135deg, var(--primary-alpha-10) 0%, var(--primary-alpha-05) 100%);
    border: 1px solid var(--primary-alpha-20);
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    max-width: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;
}

.editable-tag:hover {
    background: linear-gradient(135deg, var(--primary-alpha-20) 0%, var(--primary-alpha-10) 100%);
    border-color: var(--primary-alpha-40);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.editable-tag.editing {
    background: var(--surface-color);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-alpha-20);
}

/* 特征标签的特殊样式 */
.editable-tag.feature-tag {
    background: linear-gradient(135deg, var(--secondary-alpha-10) 0%, var(--secondary-alpha-05) 100%);
    border-color: var(--secondary-alpha-20);
    color: var(--secondary-color);
}

.editable-tag.feature-tag:hover {
    background: linear-gradient(135deg, var(--secondary-alpha-20) 0%, var(--secondary-alpha-10) 100%);
    border-color: var(--secondary-alpha-40);
}

/* 编辑输入框样式 */
.tag-edit-input {
    border: none;
    background: transparent;
    outline: none;
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
    width: 100%;
    min-width: 40px;
    max-width: 60px;
}

/* 字段聚焦时的增强效果 */
.editable-field-compact:focus {
    transform: scale(1.01);
    z-index: 10;
    position: relative;
}

.product-info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 10px; /* 适当增加间距 */
    padding: 10px 12px; /* 适当增加内边距以提高舒适度 */
    background: linear-gradient(135deg, var(--gray-50) 0%, rgba(248, 250, 252, 0.5) 100%);
    border-radius: 8px; /* 进一步减小圆角 */
    border: 1px solid var(--border-light);
    transition: all 0.2s ease;
}

.product-info-item:hover {
    background: linear-gradient(135deg, var(--primary-alpha-05) 0%, rgba(59, 130, 246, 0.03) 100%);
    border-color: var(--primary-alpha-20);
    transform: translateX(4px);
}

.product-info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    width: 100px; /* 适当增加标签宽度以适应更宽的卡片 */
    font-weight: 600;
    color: var(--text-primary);
    flex-shrink: 0;
    font-size: 12px; /* 稍微增加字体以提高可读性 */
    display: flex;
    align-items: center;
    gap: 4px; /* 进一步减小间距 */
}

.info-label::before {
    content: '';
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
}

.info-value {
    flex: 1;
    min-width: 0;
}

.editable-field {
    width: 100%;
    padding: 8px 10px; /* 适当增加内边距以提高舒适度 */
    border: 2px solid var(--border-light);
    border-radius: 4px; /* 进一步减小圆角 */
    font-size: 13px; /* 增加字体以提高可读性 */
    font-weight: 500;
    color: var(--text-primary);
    background: var(--surface-color);
    transition: all 0.2s ease;
    line-height: 1.2; /* 适当增加行高以提高可读性 */
}

.editable-field:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-alpha-10);
    background: white;
}

.editable-field:disabled {
    background: linear-gradient(135deg, var(--gray-50) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-color: var(--border-light);
    color: var(--text-secondary);
    cursor: not-allowed;
}

.editable-field.textarea {
    min-height: 80px;
    resize: vertical;
}

/* 单行字段样式 - 确保内容在一行显示 */
.single-line-field {
    white-space: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
    text-overflow: ellipsis;
    resize: none;
    height: auto;
    min-height: 30px; /* 适当增加最小高度 */
    line-height: 1.2;
    padding: 8px 10px; /* 与editable-field保持一致 */
}

/* 单行字段聚焦时的样式 */
.single-line-field:focus {
    white-space: nowrap;
    overflow-x: auto;
    text-overflow: clip;
}

/* 为单行字段添加滚动条样式 */
.single-line-field::-webkit-scrollbar {
    height: 4px;
}

.single-line-field::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 2px;
}

.single-line-field::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 2px;
}

.single-line-field::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* 单行字段禁用状态 */
.single-line-field:disabled {
    white-space: nowrap;
    overflow-x: auto;
    text-overflow: ellipsis;
}

.product-analysis-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px; /* 进一步减小按钮间距 */
    padding: 8px 12px; /* 进一步减小操作区域内边距 */
    border-top: 1px solid var(--border-light);
    background: linear-gradient(135deg, var(--gray-50) 0%, rgba(248, 250, 252, 0.8) 100%);
}

.primary-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border: none;
    padding: 8px 16px; /* 进一步减小按钮内边距 */
    border-radius: 8px; /* 进一步减小圆角 */
    font-weight: 600;
    font-size: 12px; /* 进一步减小字体 */
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px; /* 进一步减小图标间距 */
    box-shadow: 0 2px 8px var(--primary-alpha-20); /* 减小阴影 */
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.primary-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 24px var(--primary-alpha-30);
}

.primary-btn:hover::before {
    left: 100%;
}

.primary-btn:active {
    transform: translateY(0);
}

.edit-product-btn {
    background: var(--surface-color);
    border: 2px solid var(--border-light);
    padding: 8px 16px; /* 进一步减小按钮内边距 */
    border-radius: 8px; /* 进一步减小圆角 */
    font-weight: 600;
    font-size: 12px; /* 进一步减小字体 */
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px; /* 进一步减小图标间距 */
    color: var(--text-primary);
    transition: all 0.2s ease;
}

.edit-product-btn:hover {
    background: var(--primary-alpha-05);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.step-item {
    display: flex;
    flex-direction: column;
    padding: 12px;
    background-color: var(--background-color);
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
}

.step-label {
    font-weight: 500;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    color: var(--primary-color);
}

.step-label i {
    margin-right: 8px;
}

.step-content {
    padding-left: 24px;
    line-height: 1.5;
}

.step-content ul {
    margin: 5px 0;
    padding-left: 20px;
}

.step-content ul li {
    margin-bottom: 4px;
}

.analysis-complete .step-item {
    border-left-color: #4caf50;
}

.analysis-complete .step-label {
    color: #4caf50;
}

/* 博主推荐卡片样式 */
.creator-showcase {
    margin-bottom: 20px;
}

/* 博主列表样式 - 优化为列表布局 */
.creators-recommendation-container {
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow-color);
    margin: 10px auto;
    overflow: hidden;
    max-width: 1200px;
    width: 100%;
}

.creators-list-header {
    background-color: var(--gray-50);
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
}

.list-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.list-count {
    font-size: 14px;
    font-weight: 400;
    color: var(--text-secondary);
}

.creators-row-list {
    padding: 15px;
    max-height: 600px;
    overflow-y: auto;
}

/* 行式网红列表项样式 */
.creator-row-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 12px;
    background-color: var(--surface-color);
    transition: all 0.2s ease;
    gap: 15px;
}

.creator-row-item:hover {
    box-shadow: 0 4px 12px var(--shadow-color);
    transform: translateY(-1px);
}

.creator-row-checkbox {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.creator-row-avatar {
    flex-shrink: 0;
}

.creator-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.creator-row-info {
    flex: 1;
    min-width: 0;
}

.creator-row-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 5px;
}

.creator-row-stats {
    display: flex;
    gap: 15px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.stat-item {
    font-size: 13px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 4px;
}

.creator-row-tags {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.creator-row-description {
    flex: 2;
    min-width: 200px;
    padding: 0 15px;
}

.creator-row-description p {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.4;
}

.creator-row-relevance {
    flex-shrink: 0;
    text-align: center;
    padding: 8px 12px;
    border-radius: 6px;
    min-width: 80px;
}

/* 优化达人选择区域 - 紧凑型布局 */
.creator-selection {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 2;
    padding: 0;
    background: none;
    border: none;
}

.creator-checkbox {
    display: none;
}

.creator-checkbox-label {
    width: 18px;
    height: 18px;
    border: 2px solid #bdbdbd;
    border-radius: 3px;
    cursor: pointer;
    position: relative;
    background-color: var(--surface-color);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.creator-checkbox:checked + .creator-checkbox-label {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.creator-checkbox:checked + .creator-checkbox-label::after {
    content: '\2714';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* 优化达人信息容器 - 紧凑型垂直布局 */
.creator-info-container {
    flex: 1;
    padding: 12px;
    padding-top: 30px; /* 为复选框留出空间 */
    display: flex;
    flex-direction: column;
    height: 100%;
}

.creator-basic-details {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 8px;
    text-align: center;
}

.creator-list-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    margin: 0 auto 8px;
    object-fit: cover;
    flex-shrink: 0;
}

.creator-list-info {
    flex: 1;
    width: 100%;
}

.creator-list-name {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 4px;
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.creator-list-stats {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 6px;
    font-size: 11px;
    color: var(--text-secondary);
    flex-wrap: wrap;
}

.creator-list-stats span {
    display: flex;
    align-items: center;
}

.creator-list-stats span i {
    margin-right: 2px;
}

/* 优化标签和相关度显示 */
.creator-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-bottom: 6px;
    justify-content: center;
}

.creator-tag {
    background-color: var(--primary-pale);
    color: var(--primary-hover);
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
}

.creator-relevance {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    margin-top: auto;
    padding: 4px 8px;
    background-color: var(--gray-50);
    border-radius: 12px;
    font-size: 11px;
}

.relevance-score {
    font-size: 14px;
    font-weight: 700;
}

.relevance-text {
    font-size: 10px;
    font-weight: 500;
    white-space: nowrap;
}

.high-relevance {
    color: var(--success-dark);
}

.medium-relevance {
    color: #f57c00;
}

.normal-relevance {
    color: var(--gray-600);
}

/* 在紧凑模式下隐藏描述 */
.creator-description {
    display: none; /* 在网格布局中隐藏以节省空间 */
}

.creators-action-buttons {
    display: flex;
    justify-content: space-between;
    padding: 15px;
    border-top: 1px solid  var(--border-color);
    background-color: var(--gray-50);
}

.select-all-btn {
    background-color: var(--surface-color);
    border: 1px solid  var(--border-color);
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.select-all-btn i {
    margin-right: 6px;
}

.generate-emails-btn {
    padding: 8px 16px;
}

/* 邮件生成相关样式 - 优化支持50个达人 */
.email-generation-container {
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow-color);
    margin: 10px auto;
    overflow: hidden;
    max-width: 1200px; /* 增加最大宽度 */
    width: 100%;
}

.email-generation-header {
    background-color: var(--gray-50);
    padding: 12px 15px;
    border-bottom: 1px solid  var(--border-color);
}

.email-generation-header h4 {
    margin: 0;
    display: flex;
    align-items: center;
    font-size: 16px;
    color: var(--text-color);
}

.email-generation-header h4 i {
    margin-right: 8px;
    color: var(--primary-color);
}

.email-generation-content {
    padding: 15px;
}

.selected-creators-section {
    margin-bottom: 20px;
}

.section-title {
    font-weight: 500;
    margin-bottom: 10px;
    color: var(--text-color);
}

.selected-creators-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.selected-creator-item {
    display: flex;
    align-items: center;
    gap: 6px;
    background-color: var(--gray-50);
    border: 1px solid var(--border-color);
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    transition: all 0.2s ease;
}

.selected-creator-item:hover {
    background-color: var(--gray-100);
}

.quick-creator-checkbox {
    width: 14px;
    height: 14px;
    margin: 0;
    cursor: pointer;
}

.quick-creator-label {
    cursor: pointer;
    font-weight: 500;
    color: var(--text-color);
    margin: 0;
}

.selected-creator-item:has(.quick-creator-checkbox:not(:checked)) {
    opacity: 0.6;
    background-color: var(--gray-100);
}

.selected-creator-item:has(.quick-creator-checkbox:not(:checked)) .quick-creator-label {
    text-decoration: line-through;
    color: var(--text-secondary);
}

/* 博主切换功能样式 */
.creator-switch-section {
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--gray-50);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.creator-switch-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.creator-switch-option {
    padding: 8px 16px;
    border: 2px solid var(--border-color);
    border-radius: 20px;
    background-color: var(--surface-color);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.creator-switch-option:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.creator-switch-option.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    box-shadow: 0 2px 12px rgba(33, 150, 243, 0.3);
}

.creator-switch-option.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
}

.email-preview-section {
    margin-bottom: 20px;
}

.email-preview-container {
    border: 1px solid  var(--border-color);
    border-radius: 8px;
    overflow: hidden;
}

.email-preview-header {
    background-color: var(--gray-50);
    padding: 12px 15px;
    border-bottom: 1px solid  var(--border-color);
}

.email-field {
    display: flex;
    margin-bottom: 8px;
}

.email-field:last-child {
    margin-bottom: 0;
}

.email-field-label {
    width: 80px;
    font-weight: 500;
    color: var(--text-secondary);
}

.email-field-value {
    flex: 1;
}

.email-subject-input {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid  var(--border-color);
    border-radius: 4px;
    font-size: 14px;
}

.email-preview-body {
    padding: 15px;
}

.email-body-input {
    width: 100%;
    padding: 10px;
    border: 1px solid  var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
}

.email-generation-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 12px 15px;
    border-top: 1px solid  var(--border-color);
    background-color: var(--gray-50);
}

.regenerate-email-btn {
    background-color: var(--surface-color);
    border: 1px solid  var(--border-color);
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.regenerate-email-btn i {
    margin-right: 6px;
}

.send-email-btn {
    padding: 8px 16px;
}

/* 邮件发送成功提示样式 */
.email-sent-success {
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow-color);
    padding: 20px;
    text-align: center;
    margin: 10px auto; /* 居中对齐 */
    max-width: 600px; /* 调整最大宽度 */
    width: 100%; /* 统一宽度 */
}

.success-icon {
    width: 64px;
    height: 64px;
    background-color: var(--success-pale);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
}

.success-icon i {
    font-size: 32px;
    color: var(--success-dark);
}

.success-message h4 {
    font-size: 18px;
    margin-bottom: 8px;
    color: var(--success-dark);
}

.success-message p {
    font-size: 15px;
    color: var(--text-secondary);
    margin-bottom: 15px;
}

.success-details {
    background-color: var(--gray-50);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    text-align: left;
}

.detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
    color: var(--text-secondary);
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-item i {
    margin-right: 8px;
    color: var(--primary-color);
}

.next-steps {
    text-align: left;
    margin-bottom: 20px;
}

.next-steps h5 {
    font-size: 15px;
    margin-bottom: 10px;
    color: var(--text-color);
}

.next-steps ul {
    padding-left: 20px;
    margin: 0;
}

.next-steps li {
    margin-bottom: 6px;
    font-size: 14px;
    color: var(--text-secondary);
}

.success-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.view-outreach-btn, .continue-ai-btn {
    padding: 10px 20px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.continue-ai-btn {
    background-color: var(--surface-color);
    border: 1px solid  var(--border-color);
    color: var(--text-color);
}

.continue-ai-btn i, .view-outreach-btn i {
    margin-right: 8px;
}

.creator-card-large {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 5px var(--black-alpha-10);
    transition: all 0.2s ease;
}

.creator-card-large:hover {
    box-shadow: 0 4px 12px var(--black-alpha-15);
    transform: translateY(-2px);
}

.creators-container {
    max-width: 100%;
}

.contact-creator-btn:hover {
    background-color: var(--primary-dark) !important;
    transform: translateY(-1px);
}

.view-more-btn:hover {
    background-color: #e8e8e8 !important;
    transform: translateY(-1px);
}

/* 访问频道链接 */
a[href^="#"] {
    display: inline-flex;
    align-items: center;
    text-decoration: none;
}

/* 额外视觉增强 */
.ai-message .message-content p {
    margin-bottom: 10px;
}

.ai-message .message-content p:last-child {
    margin-bottom: 0;
}

/* JSON折叠相关样式 */
.json-container {
    margin-top: 10px;
}

.json-toggle {
    background-color: #f0f4f8;
    color: var(--primary-color);
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: background-color 0.2s ease;
}

.json-toggle:hover {
    background-color: var(--primary-pale);
}

.json-toggle i {
    margin-right: 6px;
}

.json-toggle i:last-child {
    margin-left: auto;
    margin-right: 0;
    transition: transform 0.2s ease;
}

.json-toggle.active i:last-child {
    transform: rotate(180deg);
}

/* 建联记录样式 */
.outreach-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    overflow-x: hidden; /* 防止水平滚动 */
    display: flex;
    flex-direction: column;
    gap: 16px;
    position: relative;
    background-color: var(--background-color);
    background-image: radial-gradient(circle at 25px 25px, rgba(25, 118, 210, 0.03) 2px, transparent 0),
                      radial-gradient(circle at 75px 75px, rgba(25, 118, 210, 0.02) 2px, transparent 0);
    background-size: 100px 100px;
    width: 100%;
    box-sizing: border-box; /* 确保padding不增加宽度 */
    height: calc(100vh - var(--header-height)); /* 明确设置高度 */
    /* 滚动优化 */
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

.outreach-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    background-color: var(--surface-color);
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 1px 4px var(--black-alpha-05);
}

.page-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
}

.page-title:before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 16px;
    background: linear-gradient(to bottom, var(--primary-color), #64b5f6);
    margin-right: 10px;
    border-radius: 2px;
}

.status-tabs {
    display: flex;
    margin-bottom: 16px;
    background-color: var(--surface-color);
    border-radius: 8px;
    padding: 4px;
    box-shadow: 0 1px 4px var(--black-alpha-05);
}

.status-tab {
    padding: 8px 16px;
    cursor: pointer;
    font-size: 13px;
    position: relative;
    color: var(--text-secondary);
    border-radius: 4px;
    transition: all 0.2s ease;
    flex: 1;
    text-align: center;
    font-weight: 500;
}

.status-tab:hover {
    background-color: var(--gray-50);
    color: var(--primary-color);
}

.status-tab.active {
    color: var(--text-color);
    font-weight: 500;
    background: linear-gradient(135deg, var(--primary-color), #2196f3);
    box-shadow: 0 1px 3px var(--primary-alpha-20);
}

.status-tab.active::after {
    display: none;
}

.outreach-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.outreach-item {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 16px;
    display: flex;
    align-items: center;
    border: 1px solid #eaeef2;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 12px;
    box-shadow: 0 2px 6px var(--black-alpha-03);
    min-height: 80px;
}

.outreach-item:last-child {
    margin-bottom: 0;
}

.outreach-item:hover {
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
    border-color: var(--border-color);
    background-color: var(--hover-bg);
}

.outreach-item:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background: linear-gradient(to bottom, var(--primary-color), #64b5f6);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.outreach-item:hover:before {
    opacity: 1;
}

.outreach-item.active {
    border-color: var(--border-color);
    background-color: var(--surface-color);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.08);
}

.outreach-item.active:before {
    opacity: 1;
    width: 4px;
}

.outreach-creator {
    display: flex;
    align-items: center;
    flex: 2;
    min-width: 240px;
}

.outreach-creator .creator-avatar {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 2px solid #f0f0f0;
    transition: all 0.2s ease;
}

.outreach-item:hover .creator-avatar {
    transform: scale(1.05);
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
}

.creator-basic-info {
    margin-left: 16px;
    flex: 1;
    min-width: 0;
}

.creator-name {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 4px;
    color: var(--text-color);
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.creator-name:after {
    content: '\eb80';
    font-family: 'remixicon';
    color: var(--primary-color);
    margin-left: 5px;
    font-size: 14px;
    display: none;
}

.verified .creator-name:after {
    display: inline-block;
}

.creator-stats {
    display: flex;
    gap: 12px;
    margin-bottom: 6px;
}

.creator-stats span {
    font-size: 13px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 4px;
    background-color: var(--gray-50);
    padding: 4px 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.creator-insights {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.insight-item {
    font-size: 12px;
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 4px;
    background-color: var(--surface-color);
    padding: 4px 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    white-space: nowrap;
    border: 1px solid var(--border-color);
}

.outreach-item:hover .insight-item {
    transform: translateY(-1px);
    background-color: var(--hover-bg);
}

.insight-item i {
    font-size: 12px;
    color: var(--primary-color);
    opacity: 0.9;
}

.insight-item.success {
    background-color: var(--success-pale);
    color: var(--success-dark);
}

.insight-item.success i {
    color: var(--success-dark);
}

.insight-item.warning {
    background-color: var(--warning-bg, var(--text-inverse)8e1);
    color: #ff8f00;
}

.insight-item.warning i {
    color: #ff8f00;
}

/* 批量操作相关样式 */
.batch-toolbar {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 14px 18px;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.06);
    animation: slide-down 0.25s ease;
    border: 1px solid  var(--border-color);
}

@keyframes slide-down {
    from { transform: translateY(-15px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.batch-info {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-color);
    background-color: var(--gray-50);
    padding: 6px 12px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.batch-info:before {
    content: '\eb80';
    font-family: 'remixicon';
    color: var(--primary-color);
    font-size: 14px;
}

.batch-actions {
    display: flex;
    gap: 10px;
}

.batch-action-btn {
    padding: 8px 14px;
    border-radius: 10px;
    font-size: 13px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.25s ease;
    border: 1px solid  var(--border-color);
    background-color: var(--gray-50);
    color: var(--primary-color);
    box-shadow: 0 1px 3px rgba(0,0,0,0.03);
}

.batch-action-btn:hover {
    background-color: var(--primary-pale);
    transform: translateY(-1px);
    border-color: #bbdefb;
    box-shadow: 0 3px 6px rgba(0,0,0,0.08);
}

.batch-action-btn.cancel-batch {
    background-color: var(--gray-100);
    color: var(--text-secondary);
    border-color: var(--border-color);
}

.batch-action-btn.cancel-batch:hover {
    background-color: var(--gray-200)eee;
    color: var(--text-primary);
}

.item-checkbox {
    display: none;
    margin-right: 14px;
}

.outreach-list.batch-mode .item-checkbox {
    display: block;
    animation: fade-in 0.3s ease;
}

@keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
}

.outreach-checkbox {
    display: none;
}

.item-checkbox label {
    display: block;
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    transition: all 0.2s ease;
    background-color: var(--surface-color);
}

.item-checkbox label:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-alpha-10);
}

.outreach-checkbox:checked + label {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: 0 2px 4px var(--primary-alpha-20);
}

.outreach-checkbox:checked + label:after {
    content: '\eb7b';
    font-family: 'remixicon';
    color: var(--text-color);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
}

.batch-mode-btn {
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    background-color: var(--gray-50);
    color: var(--primary-color);
}

.batch-mode-btn:hover {
    background-color: var(--primary-pale);
}

.outreach-product {
    flex: 1.2;
    padding: 0 16px;
    border-left: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 180px;
}

.outreach-product .product-img {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
    background-color: var(--gray-50);
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.08);
    transition: all 0.2s ease;
}

.outreach-item:hover .outreach-product .product-img {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0,0,0,0.08);
}

.outreach-product .product-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
}

.outreach-product .product-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex: 1;
    min-width: 0;
}

.product-name {
    font-weight: 600;
    color: var(--text-color);
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    letter-spacing: -0.2px;
    transition: all 0.2s ease;
}

.outreach-item:hover .product-name {
    color: var(--primary-color);
}

.product-description {
    font-size: 12px;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all 0.2s ease;
}

.outreach-intent {
    flex: 1.5;
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 0 16px;
    border-left: 1px solid var(--border-color);
    min-width: 200px;
}

.intent-tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    background-color: var(--primary-pale);
    color: var(--primary-hover);
    border-radius: 6px;
    font-size: 11px;
    font-weight: 500;
    max-width: fit-content;
    box-shadow: 0 1px 2px rgba(25, 118, 210, 0.08);
    transition: all 0.2s ease;
}

.intent-tag:before {
    content: '\f0f6';
    font-family: 'remixicon';
    margin-right: 5px;
    font-size: 12px;
}

.outreach-item:hover .intent-tag {
    background-color: var(--primary-light);
    box-shadow: 0 1px 3px rgba(25, 118, 210, 0.12);
}

.detail-product-info:hover .intent-tag {
    background-color: var(--primary-light);
    box-shadow: 0 1px 3px rgba(25, 118, 210, 0.12);
}

.outreach-summary {
    font-size: 11px;
    color: var(--text-secondary);
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    cursor: help;
    max-width: 100%;
}

.outreach-item:hover .outreach-summary {
    color: var(--text-primary);
}

.outreach-status {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 6px;
    align-items: center;
    min-width: 120px;
    padding: 0 12px;
}

.last-interaction-status {
    font-size: 11px;
    color: var(--text-secondary);
    font-style: italic;
    text-align: center;
    line-height: 1.3;
    max-width: 110px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: all 0.2s ease;
}

.outreach-item:hover .last-interaction-status {
    color: var(--text-primary);
}

.status-tag {
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 1px 2px var(--shadow-color);
    transition: all 0.2s ease;
    letter-spacing: -0.2px;
}

.outreach-item:hover .status-tag {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.08);
}

.status-tag:before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-in-progress {
    background-color: var(--primary-pale);
    color: var(--primary-hover);
}

.status-in-progress:before {
    background-color: var(--primary-dark);
    box-shadow: 0 0 0 2px var(--primary-alpha-20);
}

.status-confirmed {
    background-color: var(--success-pale);
    color: var(--success-dark);
}

.status-confirmed:before {
    background-color: var(--success-dark);
    box-shadow: 0 0 0 2px rgba(46, 125, 50, 0.2);
}

.status-promoting {
    background-color: var(--warning-bg, var(--text-inverse)8e1);
    color: #ff8f00;
}

.status-promoting:before {
    background-color: #ff8f00;
    box-shadow: 0 0 0 2px rgba(255, 143, 0, 0.2);
}

.status-completed {
    background-color: var(--surface-hover, #f5f5f5);
    color: #616161;
}

.status-completed:before {
    background-color: #616161;
    box-shadow: 0 0 0 2px rgba(97, 97, 97, 0.2);
}

.outreach-date {
    flex: 0.8;
    font-size: 12px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
    padding: 0 8px;
}

.last-contact, .created-date {
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
}

.outreach-item:hover .last-contact,
.outreach-item:hover .created-date {
    color: var(--text-primary);
}

.last-contact:before, .created-date:before {
    font-family: 'remixicon';
    margin-right: 6px;
    font-size: 14px;
    color: var(--primary-color);
}

.last-contact:before {
    content: '\f053';
}

.created-date:before {
    content: '\f04e';
}

.outreach-actions {
    flex: 0.6;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    min-width: 80px;
    padding-left: 8px;
}

.action-btn {
    height: 28px;
    padding: 0 8px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    background: transparent;
    border: 1px solid var(--border-color);
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s ease;
    font-size: 11px;
    font-weight: 500;
    white-space: nowrap;
}

.detail-btn {
    background: var(--primary-pale);
    color: var(--primary-color);
    border-color: var(--primary-alpha-30);
}

.action-btn:hover {
    background-color: var(--hover-bg);
    color: var(--primary-color);
    border-color: var(--primary-alpha-50);
    transform: translateY(-1px);
}

.detail-btn:hover {
    background: var(--primary-color);
    color: var(--surface-color);
    border-color: var(--primary-color);
    box-shadow: 0 2px 6px var(--primary-alpha-20);
}

.action-btn {
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    background-color: var(--surface-color);
    border: 1px solid  var(--border-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    min-width: 120px;
    justify-content: center;
    white-space: nowrap;
}

.action-btn:hover {
    background-color: var(--gray-50);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.action-btn:first-child {
    background-color: var(--primary-pale);
    color: var(--primary-hover);
    border-color: #bbdefb;
    min-width: 140px;
}

.action-btn:first-child:hover {
    background-color: var(--primary-light);
}

/* 详情侧边栏 */
.outreach-detail {
    position: fixed;
    right: 0;
    top: var(--header-height);
    bottom: 0;
    width: 50%; /* 修改为页面总宽度的50% */
    background-color: var(--surface-color);
    border-left: 1px solid  var(--border-color);
    box-shadow: -2px 0 16px rgba(0, 0, 0, 0.08);
    z-index: 100;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    animation: slide-in 0.3s ease forwards;
    overflow: hidden; /* 防止双滚动条 */
}

/* 详情页滚动容器 */
.detail-scroll-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

@keyframes slide-in {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

.detail-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    align-items: center;
    background-color: var(--surface-color);
    position: relative;
    z-index: 10;
    flex-shrink: 0;
}

.close-detail {
    position: absolute;
    top: 14px;
    right: 14px;
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--surface-color);
    border: 1px solid  var(--border-color);
    cursor: pointer;
    font-size: 18px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.25s ease;
    color: var(--text-light);
    z-index: 5;
}

.close-detail:hover {
    background-color: var(--hover-bg);
    transform: translateY(-2px) rotate(90deg);
    color: var(--primary-color);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* 合并卡片样式 */
.combined-info-card {
    background-color: var(--surface-color);
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.06);
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    border: 1px solid var(--border-color);
}

.creator-product-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 32px; /* 进一步增加间距 */
    min-height: 100px; /* 确保最小高度 */
}

.creator-section {
    display: flex;
    align-items: flex-start;
    gap: 20px; /* 增加间距 */
    flex: 1.5; /* 给达人信息更多空间 */
}

.product-section {
    display: flex;
    align-items: flex-start;
    gap: 18px; /* 增加间距 */
    flex: 1;
    padding-left: 24px; /* 增加左边距 */
    border-left: 2px solid var(--border-color);
    position: relative;
}

/* 建联详情弹窗中的商品图片样式 */
.product-section .product-img {
    width: 80px; /* 进一步增加商品图片尺寸 */
    height: 80px; /* 进一步增加商品图片尺寸 */
    border-radius: 16px; /* 增加圆角 */
    overflow: hidden;
    flex-shrink: 0;
    background-color: var(--background-color);
    border: 2px solid var(--border-color);
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.product-section .product-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 14px;
}

.product-section .product-img:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0,0,0,0.12);
}

/* 商品信息样式 */
.product-section .product-info {
    display: flex;
    flex-direction: column;
    gap: 6px;
    flex: 1;
}

.product-section .product-name {
    font-size: 16px;
    font-weight: 700;
    color: var(--text-color);
    line-height: 1.3;
    margin-bottom: 4px;
}

.product-section .product-description {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.4;
    background-color: var(--gray-50);
    padding: 6px 10px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.collaboration-details {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    justify-content: space-between;
    align-items: center;
    padding-top: 16px;
    border-top: 2px solid var(--border-color);
    margin-top: 4px;
}

.creator-avatar-large {
    width: 90px; /* 进一步增加头像尺寸 */
    height: 90px; /* 进一步增加头像尺寸 */
    border-radius: 20px; /* 增加圆角 */
    object-fit: cover;
    box-shadow: 0 6px 16px rgba(0,0,0,0.1);
    border: 3px solid var(--surface-color);
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.creator-avatar-large:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

.detail-creator-info {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    gap: 8px;
}

.detail-creator-name {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 0;
    color: var(--text-color);
    display: flex;
    align-items: center;
    line-height: 1.3;
}

.detail-creator-name:after {
    content: '\eb80';
    font-family: 'remixicon';
    color: var(--primary-color);
    margin-left: 8px;
    font-size: 18px;
}

.detail-creator-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    font-size: 14px;
    color: var(--text-light);
    margin-top: 4px;
}

.detail-creator-stats span {
    display: flex;
    align-items: center;
    background-color: var(--gray-50);
    padding: 4px 8px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.detail-creator-stats span i {
    margin-right: 6px;
    font-size: 16px;
    color: var(--primary-color);
}

/* 达人标签样式 */
.creator-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 12px;
}

.creator-tag {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 13px;
    font-weight: 600;
    transition: all 0.2s ease;
    white-space: nowrap;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.creator-tag.tech {
    background-color: #e3f2fd;
    color: #1976d2;
    border: 1px solid #bbdefb;
}

.creator-tag.ai {
    background-color: #f3e5f5;
    color: #7b1fa2;
    border: 1px solid #ce93d8;
}

.creator-tag.review {
    background-color: #e8f5e8;
    color: #388e3c;
    border: 1px solid #a5d6a7;
}

.creator-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.12);
}

.creator-insights-detail {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.insight-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background-color: var(--gray-50);
    border-radius: 12px;
    font-size: 13px;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.insight-item:hover {
    background-color: var(--primary-alpha-05);
    border-color: var(--primary-alpha-20);
    color: var(--primary-color);
}

.insight-item i {
    color: var(--primary-color);
    font-size: 14px;
}

.collaboration-info {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.intent-tag {
    padding: 6px 12px;
    background-color: var(--primary-alpha-10);
    color: var(--primary-color);
    border-radius: 12px;
    font-size: 13px;
    font-weight: 600;
    border: 1px solid var(--primary-alpha-20);
    transition: all 0.2s ease;
}

.intent-tag:hover {
    background-color: var(--primary-alpha-20);
    transform: translateY(-1px);
}

.channel-status-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 16px;
    flex-wrap: wrap;
}

.detail-product-info {
    background-color: var(--surface-color);
    border-radius: 10px;
    padding: 12px;
    margin: 12px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.03);
    border: 1px solid var(--border-color);
    transition: all 0.25s ease;
}

.detail-product-info:hover {
    box-shadow: 0 3px 8px rgba(0,0,0,0.06);
    transform: translateY(-1px);
}

.detail-product-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.detail-product-header .product-img {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    overflow: hidden;
    flex-shrink: 0;
    background-color: var(--gray-50);
    border: 1px solid var(--primary-alpha-10);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.06);
    transition: all 0.25s ease;
    position: relative;
    z-index: 2;
}

.detail-product-info:hover .product-img {
    transform: scale(1.05);
    box-shadow: 0 3px 6px rgba(0,0,0,0.1);
}

.detail-product-header .product-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
}

.detail-product-header .product-info {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.detail-product-header .product-name {
    font-weight: 600;
    color: var(--text-color);
    font-size: 15px;
    letter-spacing: -0.2px;
    transition: all 0.2s ease;
}

.detail-product-info:hover .product-name {
    color: var(--primary-color);
}

.detail-product-header .product-description {
    font-size: 12px;
    color: var(--text-secondary);
    background-color: var(--gray-50);
    padding: 2px 8px;
    border-radius: 5px;
    display: inline-block;
    max-width: 100%;
    transition: all 0.2s ease;
}

.collaboration-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 6px;
}

.budget-tag {
    display: inline-flex;
    align-items: center;
    padding: 5px 12px;
    background-color: var(--success-pale);
    color: var(--success-dark);
    border-radius: 10px;
    font-size: 13px;
    font-weight: 500;
    box-shadow: 0 1px 3px var(--success-alpha-10);
    transition: all 0.25s ease;
}

.budget-tag:before {
    content: '\f1b3';
    font-family: 'remixicon';
    margin-right: 6px;
    font-size: 14px;
}

.detail-product-info:hover .budget-tag {
    background-color: var(--success-light);
    box-shadow: 0 2px 5px rgba(46, 125, 50, 0.15);
}

.next-step-suggestion {
    background-color: var(--warning-pale);
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.04);
    border: 1px solid #ffecb3;
    transition: all 0.25s ease;
}

.next-step-suggestion:hover {
    box-shadow: 0 4px 10px rgba(0,0,0,0.08);
    transform: translateY(-2px);
}

.suggestion-header {
    font-weight: 600;
    color: #ff8f00;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
}

.suggestion-header i {
    font-size: 18px;
}

.suggestion-content {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 12px;
    line-height: 1.5;
}

.next-step-btn {
    background-color: #ff8f00;
    color: var(--text-color);
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.25s ease;
    box-shadow: 0 2px 5px rgba(255, 143, 0, 0.2);
}

.next-step-btn:hover {
    background-color: #f57c00;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 143, 0, 0.3);
}

.suggestion-content {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 12px;
    line-height: 1.5;
}

.next-step-btn {
    background-color: #ff8f00;
    color: var(--text-color);
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.next-step-btn:hover {
    background-color: #f57c00;
    transform: translateY(-2px);
}

.channel-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background-color: var(--primary-pale);
    border-radius: 20px;
    width: fit-content;
    transition: all 0.2s ease;
    font-weight: 500;
}

.channel-link:hover {
    background-color: var(--primary-light);
    transform: translateY(-2px);
}

.detail-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-change-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--surface-color);
    border: 1px solid  var(--border-color);
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.2s ease;
    color: var(--text-secondary);
}

.status-change-btn:hover {
    background-color: var(--gray-100);
    transform: translateY(-2px);
    color: var(--primary-color);
}

.status-dropdown {
    position: absolute;
    top: calc(100% + 5px);
    right: 0;
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px var(--shadow-hover);
    width: 120px;
    z-index: 1000;
    overflow: hidden;
    display: none;
}

.detail-status:hover .status-dropdown {
    display: block;
    animation: fadeIn 0.2s ease;
}

.status-option {
    padding: 10px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--border-light);
}

.status-option:last-child {
    border-bottom: none;
}

.status-option:hover {
    background-color: var(--gray-50);
    color: var(--primary-color);
}

/* 旧的标签页样式已移除 - 现在使用连续滚动布局 */

.communication-timeline {
    display: flex;
    flex-direction: column;
    gap: 18px;
    position: relative;
    padding-left: 32px;
    margin-bottom: 20px;
}

.communication-timeline:before {
    content: '';
    position: absolute;
    left: 16px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, var(--primary-color) 0%, #64b5f6 100%);
    opacity: 0.5;
}

.timeline-item {
    display: flex;
    gap: 12px;
    position: relative;
    transition: all 0.2s ease;
}

.timeline-item:hover {
    transform: translateY(-2px);
}

.timeline-icon {
    position: absolute;
    left: -32px;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: var(--surface-color);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 6px rgba(0,0,0,0.12);
    color: var(--primary-color);
    z-index: 1;
    border: 1px solid var(--primary-alpha-20);
}

.timeline-content {
    flex: 1;
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 16px;
    position: relative;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.timeline-item:hover .timeline-content {
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    border-color: var(--primary-light);
}

.timeline-title {
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-color);
    font-size: 15px;
}

.timeline-meta {
    font-size: 12px;
    color: var(--text-light);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    background-color: var(--background-color);
    padding: 4px 8px;
    border-radius: 4px;
    width: fit-content;
}

.timeline-meta:before {
    content: '\f04e';
    font-family: 'remixicon';
    margin-right: 6px;
    font-size: 14px;
    color: var(--primary-color);
}

.timeline-body {
    font-size: 14px;
    color: var(--text-color);
    line-height: 1.5;
    background-color: var(--surface-color);
    padding: 12px;
    border-radius: 8px;
    border-left: 2px solid var(--primary-color);
}

/* 邮件内容和翻译按钮样式 */
.email-content {
    position: relative;
    padding: 16px;
    background-color: var(--surface-color);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    margin: 12px 0;
}

.email-content .original-text {
    margin: 0;
    line-height: 1.6;
    color: var(--text-color);
    font-size: 14px;
    padding-right: 40px; /* 为翻译按钮留出空间 */
}

.translate-btn {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 32px;
    height: 32px;
    border: none;
    background-color: var(--primary-color);
    color: var(--surface-color);
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.translate-btn:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.translated-text {
    margin-top: 16px;
    padding: 16px;
    background-color: var(--gray-50);
    border-radius: 12px;
    border-left: 4px solid var(--success-color);
    font-style: italic;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.translated-text p {
    margin: 0;
    line-height: 1.6;
    font-size: 14px;
}

.timeline-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 16px;
    flex-wrap: wrap;
}

.timeline-action-btn {
    font-size: 13px;
    padding: 8px 16px;
    border-radius: 20px;
    background-color: var(--gray-50);
    border: 1px solid var(--border-color);
    color: var(--primary-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
    font-weight: 500;
    white-space: nowrap;
}

.timeline-action-btn:hover {
    background-color: var(--primary-light);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.add-record {
    margin-top: 20px;
    border-top: 1px solid var(--border-color);
    background-color: var(--surface-color);
    border-radius: 10px;
    padding: 16px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.04);
    margin-bottom: 16px;
}

.add-record h4 {
    font-size: 16px;
    margin-bottom: 12px;
    color: var(--text-color);
    font-weight: 600;
    display: flex;
    align-items: center;
}

.add-record h4:before {
    content: '\ea7a';
    font-family: 'remixicon';
    margin-right: 6px;
    color: var(--primary-color);
    font-size: 18px;
}

.record-input {
    width: 100%;
    min-height: 80px;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid  var(--border-color);
    resize: vertical;
    font-family: inherit;
    font-size: 14px;
    margin-bottom: 12px;
    transition: all 0.2s ease;
    line-height: 1.5;
}

.record-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-alpha-10);
}

.record-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    align-items: center;
}

.record-type-btn {
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    background-color: var(--surface-color);
    border: 1px solid  var(--border-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    font-weight: 500;
    color: var(--text-light);
}

.record-type-btn:hover {
    background-color: var(--hover-bg);
    transform: translateY(-2px);
    color: var(--primary-color);
}

.record-type-btn i {
    font-size: 16px;
    color: var(--primary-color);
}

.save-record-btn {
    margin-left: auto;
    padding: 8px 20px;
    border-radius: 8px;
    font-size: 14px;
    background: linear-gradient(135deg, var(--primary-color), #2196f3);
    color: var(--text-inverse);
    border: none;
    cursor: pointer;
    font-weight: 500;
    box-shadow: 0 2px 6px var(--primary-alpha-30);
    transition: all 0.2s ease;
}

.save-record-btn:hover {
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(25, 118, 210, 0.4);
}

/* 近期内容模块隐藏 */
.recent-content-section {
    display: none; /* 隐藏近期内容模块 */
}

/* 动态回复卡片样式 */
.reply-card {
    margin-top: 16px;
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    animation: slideDown 0.3s ease forwards;
}

.reply-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.reply-card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.reply-card-title i {
    color: var(--primary-color);
    font-size: 18px;
}

.close-reply-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-reply-btn:hover {
    background-color: var(--gray-50);
    color: var(--text-color);
}

/* AI工具选择器样式 */
.ai-tool-selector {
    margin-bottom: 16px;
}

.ai-tool-dropdown {
    position: relative;
    width: 100%;
}

.ai-tool-dropdown-btn {
    width: 100%;
    padding: 12px 16px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.2s ease;
}

.ai-tool-dropdown-btn:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3);
}

.ai-tool-dropdown-btn i {
    transition: transform 0.2s ease;
}

.ai-tool-dropdown.open .ai-tool-dropdown-btn i {
    transform: rotate(180deg);
}

.ai-tool-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    z-index: 1000;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    margin-top: 4px;
}

.ai-tool-dropdown.open .ai-tool-dropdown-menu {
    max-height: 200px;
}

.ai-tool-option {
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.ai-tool-option:hover {
    background-color: var(--gray-50);
}

.ai-tool-option.recommended {
    background-color: var(--primary-pale);
    color: var(--primary-color);
    font-weight: 500;
}

.ai-tool-option.recommended::after {
    content: "推荐";
    background-color: var(--primary-color);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: auto;
}

.ai-tool-option i {
    color: var(--primary-color);
    font-size: 16px;
}

/* 工具表单展开区域 */
.tool-form-container {
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    margin-top: 16px;
}

.tool-form-container.expanded {
    max-height: 500px;
}

.tool-form {
    padding: 16px;
    background-color: var(--gray-25);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

/* 回复卡片动画 */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 500px;
    }
}

/* 回复卡片操作按钮 */
.reply-actions {
    display: flex;
    gap: 12px;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--border-color);
}

.send-reply-btn {
    flex: 1;
    padding: 12px 20px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.send-reply-btn:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(25, 118, 210, 0.3);
}

.send-reply-btn:disabled {
    background-color: var(--gray-300);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 自定义内容输入框 */
.custom-content-input {
    width: 100%;
    min-height: 80px;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 14px;
    font-family: inherit;
    resize: vertical;
    transition: border-color 0.2s ease;
}

.custom-content-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.custom-content-input::placeholder {
    color: var(--text-secondary);
}

/* 邮件发送通知样式 */
.email-sent-notification {
    min-width: 300px;
    max-width: 400px;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
}

.notification-icon {
    width: 40px;
    height: 40px;
    background-color: var(--success-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.notification-text h4 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
}

.notification-text p {
    margin: 0;
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    margin-left: auto;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background-color: var(--gray-50);
    color: var(--text-color);
}

/* 通知动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 发送邮件卡片样式 - 隐藏固定邮件回复框 */
.send-email-card {
    display: none; /* 隐藏固定的邮件回复框 */
    margin-top: 20px;
    border-top: 1px solid var(--border-color);
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 3px 8px rgba(0,0,0,0.06);
    margin-bottom: 16px;
}

.send-email-card h4 {
    font-size: 16px;
    margin-bottom: 16px;
    color: var(--text-color);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.send-email-card h4 i {
    color: var(--primary-color);
    font-size: 18px;
}

.email-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.email-field {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.email-field label {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-secondary);
}

.email-input {
    width: 100%;
    padding: 10px 12px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    font-family: inherit;
    font-size: 14px;
    transition: all 0.2s ease;
    background-color: var(--surface-color);
}

.email-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-alpha-10);
}

.email-input[readonly] {
    background-color: var(--background-color);
    color: var(--text-light);
}

.email-content-input {
    width: 100%;
    min-height: 100px;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    resize: vertical;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    transition: all 0.2s ease;
}

.email-content-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-alpha-10);
}

.email-actions {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

.email-template-btn, .ai-assist-btn, .price-negotiation-btn, .shipping-notification-btn, .video-script-btn {
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
    font-weight: 500;
    color: var(--text-secondary);
}

.email-template-btn:hover, .ai-assist-btn:hover, .price-negotiation-btn:hover, .shipping-notification-btn:hover, .video-script-btn:hover {
    background-color: var(--hover-bg);
    transform: translateY(-1px);
    color: var(--primary-color);
    border-color: var(--primary-light);
}

/* 邮件工具按钮特殊颜色 */
.price-negotiation-btn {
    color: #059669;
    border-color: #d1fae5;
}

.price-negotiation-btn:hover {
    background-color: #ecfdf5;
    color: #047857;
    border-color: #10b981;
}

.shipping-notification-btn {
    color: #dc2626;
    border-color: #fecaca;
}

.shipping-notification-btn:hover {
    background-color: #fef2f2;
    color: #b91c1c;
    border-color: #ef4444;
}

.video-script-btn {
    color: #7c3aed;
    border-color: #ddd6fe;
}

.video-script-btn:hover {
    background-color: #f5f3ff;
    color: #6d28d9;
    border-color: #8b5cf6;
}

.send-email-btn {
    margin-left: auto;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 14px;
    background: linear-gradient(135deg, var(--primary-color), #2196f3);
    color: var(--text-inverse);
    border: none;
    cursor: pointer;
    font-weight: 600;
    box-shadow: 0 2px 6px var(--primary-alpha-30);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.send-email-btn:hover {
    background: linear-gradient(135deg, var(--primary-hover), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(25, 118, 210, 0.4);
}

/* YouTube博主界面样式 */
.influencer-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.influencer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.influencer-filters {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 120px;
}

.filter-group label {
    font-size: 13px;
    color: var(--text-light);
}

.filter-group select {
    padding: 6px 10px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    font-size: 14px;
    background: #f5f5f5;
}

.influencer-list {
    display: flex;
    flex-wrap: wrap;
    gap: 32px;
    justify-content: flex-start;
}

.influencer-card {
    background: var(--surface-color);
    border-radius: 14px;
    box-shadow: 0 2px 8px rgba(25,118,210,0.06);
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 320px;
    min-height: 320px;
    padding: 32px 24px 24px 24px;
    border: 1px solid var(--border-color);
    transition: box-shadow 0.2s, transform 0.2s;
    position: relative;
}

.influencer-card:hover {
    box-shadow: 0 8px 24px rgba(25, 118, 210, 0.13);
    transform: translateY(-4px) scale(1.02);
}

.influencer-avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 18px;
    font-size: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.influencer-info {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    margin-bottom: 18px;
}

.influencer-name {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 2px;
    text-align: center;
    word-break: break-all;
}

.influencer-meta {
    display: flex;
    gap: 12px;
    font-size: 13px;
    color: var(--text-light);
    flex-wrap: wrap;
    justify-content: center;
}

.influencer-tags {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 8px;
    justify-content: center;
}

.tag {
    background: #e3f2fd;
    color: var(--primary-color);
    padding: 5px 14px;
    border-radius: 16px;
    font-size: 13px;
    font-weight: 500;
}

.influencer-actions {
    display: flex;
    flex-direction: row;
    gap: 14px;
    width: 100%;
    justify-content: center;
    margin-top: 10px;
}

.add-outreach-btn, .view-detail-btn {
    padding: 8px 0;
    border-radius: 6px;
    font-size: 14px;
    border: none;
    cursor: pointer;
    font-weight: 600;
    width: 140px;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s;
    box-shadow: 0 1px 2px rgba(25,118,210,0.04);
}

.add-outreach-btn {
    background: var(--primary-color);
    color: var(--text-inverse);
}

.add-outreach-btn:hover {
    background: var(--primary-hover);
}

.view-detail-btn {
    background: #f5f5f5;
    color: var(--primary-color);
}

.view-detail-btn:hover {
    background: #e3f2fd;
    color: var(--primary-hover);
}

@media (max-width: 900px) {
    .influencer-list {
        justify-content: center;
    }
    .influencer-card {
        width: 95vw;
        min-width: 0;
        max-width: 360px;
    }
}

/* 仪表盘样式 */
.dashboard-container {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 20px;
    background-color: var(--background-color);
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.page-title {
    font-size: 22px;
    font-weight: 700;
    color: var(--text-color);
    letter-spacing: -0.5px;
}

.dashboard-content {
    display: flex;
    flex-direction: column;
    gap: 18px;
}

/* KPI卡片样式 */
.kpi-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
    margin-bottom: 0;
}

.kpi-card {
    background: var(--surface-color);
    padding: 20px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    position: relative;
    box-shadow: 0 3px 12px rgba(0,0,0,0.05);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    overflow: hidden;
}

.kpi-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.08);
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--primary-color), #64b5f6);
    opacity: 0.7;
}

.kpi-icon {
    width: 50px;
    height: 50px;
    background: var(--surface-elevated, var(--surface-color));
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 18px;
    margin-left: 8px;
    color: var(--text-color);
    font-size: 24px;
    box-shadow: 0 4px 10px var(--shadow-color);
    border: 1px solid var(--border-light);
}

.kpi-data {
    flex: 1;
}

.kpi-value {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 5px;
    background: linear-gradient(90deg, var(--primary-color), #64b5f6);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: -0.5px;
}

.kpi-label {
    color: var(--text-light);
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.2px;
}

.kpi-trend {
    position: absolute;
    top: 14px;
    right: 14px;
    font-size: 12px;
    font-weight: 600;
    display: flex;
    align-items: center;
    border-radius: 16px;
    padding: 4px 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.08);
}

.kpi-trend i {
    margin-right: 4px;
}

.kpi-trend.positive {
    color: var(--success-dark);
    background: #e8f5e9;
}

.kpi-trend.negative {
    color: #c62828;
    background: #ffebee;
}

/* 仪表盘主要内容区域 */
.dashboard-main-content {
    display: flex;
    gap: 20px;
    margin-top: 20px;
}

.dashboard-left-column {
    flex: 1;
    min-width: 0; /* 防止内容溢出 */
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.dashboard-right-column {
    width: 380px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 快速操作按钮 */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.quick-action-btn {
    display: flex;
    align-items: center;
    padding: 14px 16px;
    border-radius: 8px;
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-action-btn i {
    font-size: 20px;
    margin-right: 12px;
    color: var(--primary-color);
}

.quick-action-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.quick-action-btn.primary {
    background-color: var(--primary-color);
    color: var(--text-color);
    border-color: var(--primary-color);
}

.quick-action-btn.primary i {
    color: var(--text-color);
}

.quick-action-btn.primary:hover {
    background-color: var(--primary-dark);
}

/* 建联阶段指示器样式 */
.outreach-stage-indicator {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.stage-header {
    margin-bottom: 16px;
}

.stage-header h4 {
    margin: 0;
    font-size: 16px;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.stage-header h4 i {
    color: var(--primary-color);
}

.stage-progress {
    display: flex;
    align-items: center;
    gap: 12px;
    overflow-x: auto;
    padding: 8px 0;
}

.stage-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    min-width: 80px;
    position: relative;
}

.stage-item:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 12px;
    right: -18px;
    width: 24px;
    height: 2px;
    background-color: var(--gray-300);
    z-index: 1;
}

.stage-item.completed:not(:last-child)::after {
    background-color: var(--primary-color);
}

.stage-dot {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--gray-300);
    border: 2px solid var(--gray-300);
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.stage-item.completed .stage-dot {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.stage-item.completed .stage-dot::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.stage-item.active .stage-dot {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.2);
}

.stage-label {
    font-size: 12px;
    color: var(--text-secondary);
    text-align: center;
    line-height: 1.2;
}

.stage-item.completed .stage-label,
.stage-item.active .stage-label {
    color: var(--text-color);
    font-weight: 500;
}

/* 邮件意图指示器样式 */
.email-intent-indicator {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.intent-header {
    margin-bottom: 16px;
}

.intent-header h4 {
    margin: 0;
    font-size: 16px;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.intent-header h4 i {
    color: var(--primary-color);
}

.intent-status {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.intent-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    border: 1px solid;
    width: fit-content;
}

.intent-badge.active {
    background-color: #e3f2fd;
    color: #1976d2;
    border-color: #bbdefb;
}

.intent-badge.positive {
    background-color: #e8f5e8;
    color: #2e7d32;
    border-color: #c8e6c9;
}

.intent-badge.neutral {
    background-color: #fff3e0;
    color: #f57c00;
    border-color: #ffcc02;
}

.intent-badge.negative {
    background-color: #ffebee;
    color: #d32f2f;
    border-color: #ffcdd2;
}

.intent-description {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* 沟通记录和近期内容区域样式 */
.communication-content,
.recent-content-section {
    background-color: var(--surface-color);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.communication-header,
.content-header {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
}

.communication-header h4,
.content-header h4 {
    margin: 0;
    font-size: 16px;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.communication-header h4 i,
.content-header h4 i {
    color: var(--primary-color);
}

/* 邮件工具模态框样式 */
.email-tool-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1100;
    display: flex;
    align-items: center;
    justify-content: center;
}

.email-tool-modal-content {
    width: 90%;
    max-width: 500px;
    background-color: var(--surface-color);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    overflow: hidden;
    animation: modal-appear 0.3s ease;
}

.email-tool-modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--gray-50);
}

.email-tool-modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.email-tool-modal-header h3 i {
    color: var(--primary-color);
}

.email-tool-modal-body {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.email-tool-modal-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    background-color: var(--gray-50);
}

.cancel-btn {
    padding: 8px 16px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background-color: var(--surface-color);
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.cancel-btn:hover {
    background-color: var(--hover-bg);
    color: var(--text-color);
}

.generate-btn {
    padding: 8px 16px;
    border-radius: 8px;
    border: none;
    background-color: var(--primary-color);
    color: white;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.generate-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

.generate-btn:disabled {
    background-color: var(--gray-300);
    cursor: not-allowed;
    transform: none;
}

/* 批量邮件操作样式 */
.bulk-email-container {
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow-color);
    margin: 10px auto;
    overflow: hidden;
    max-width: 1200px;
    width: 100%;
}

.bulk-email-header {
    background-color: var(--gray-50);
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bulk-email-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.bulk-email-stats {
    font-size: 14px;
    color: var(--text-secondary);
}

.bulk-email-content {
    padding: 20px;
}

.email-list-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 15px;
    max-height: 500px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.email-item {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    background-color: var(--surface-color);
    transition: all 0.2s ease;
    position: relative;
}

.email-item:hover {
    box-shadow: 0 2px 8px var(--shadow-color);
    transform: translateY(-1px);
}

.email-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.email-item-left {
    display: flex;
    align-items: center;
    gap: 10px;
}

.email-checkbox {
    width: 16px;
    height: 16px;
    margin: 0;
}

.email-checkbox-label {
    cursor: pointer;
}

.email-item.email-cancelled {
    opacity: 0.6;
    background-color: var(--gray-50);
}

.email-item.email-cancelled .email-actions .send-single-email-btn {
    opacity: 0.5;
    pointer-events: none;
}

.email-status.cancelled {
    background-color: var(--gray-100);
    color: var(--text-secondary);
}

.email-recipient {
    font-weight: 600;
    font-size: 14px;
    color: var(--text-color);
}

.email-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
}

.email-status.pending {
    background-color: var(--warning-pale);
    color: var(--warning-dark);
}

.email-status.sent {
    background-color: var(--success-pale);
    color: var(--success-dark);
}

.email-status.failed {
    background-color: var(--error-pale);
    color: var(--error-dark);
}

.email-preview {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
    margin-bottom: 10px;
    max-height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.email-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.email-action-btn {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    border: 1px solid var(--border-color);
    background-color: var(--surface-color);
    color: var(--text-color);
    transition: all 0.2s ease;
}

.email-action-btn:hover {
    background-color: var(--gray-50);
}

.email-action-btn.primary {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.email-action-btn.primary:hover {
    background-color: var(--primary-hover);
}

.cancel-email-btn {
    background-color: var(--gray-100);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.cancel-email-btn:hover {
    background-color: var(--error-color);
    color: white;
    border-color: var(--error-color);
}

.bulk-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-top: 1px solid var(--border-color);
    background-color: var(--gray-50);
}

.bulk-actions-left {
    display: flex;
    gap: 10px;
    align-items: center;
}

.bulk-actions-right {
    display: flex;
    gap: 10px;
}

.bulk-action-btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
}

.bulk-action-btn.secondary {
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

.bulk-action-btn.primary {
    background-color: var(--primary-color);
    border: 1px solid var(--primary-color);
    color: white;
}

.bulk-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 响应式调整 */
@media (max-width: 1024px) {
    .dashboard-main-content {
        flex-direction: column;
    }

    .dashboard-right-column {
        width: 100%;
    }

    .quick-actions {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .quick-action-btn {
        flex: 1;
        min-width: 180px;
    }

    .email-list-container {
        grid-template-columns: 1fr;
    }

    .bulk-actions {
        flex-direction: column;
        gap: 10px;
    }

    .bulk-actions-left,
    .bulk-actions-right {
        width: 100%;
        justify-content: center;
    }
}

/* 仪表盘卡片通用样式 */
.dashboard-card {
    background: var(--surface-color);
    border-radius: 18px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.05);
    border: 1px solid var(--border-color);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    position: relative;
}

.dashboard-card:hover {
    box-shadow: 0 10px 20px rgba(0,0,0,0.08);
    transform: translateY(-5px);
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), #64b5f6);
    opacity: 0.8;
}

.card-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--surface-color);
}

.card-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    color: var(--text-color);
    letter-spacing: 0.2px;
}

.card-header h3 i {
    margin-right: 10px;
    color: var(--primary-color);
    font-size: 20px;
}

.card-action .icon-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: transparent;
    border: none;
    color: var(--text-light);
}

.card-action .icon-btn:hover {
    background: var(--hover-bg);
    color: var(--text-color);
}

.card-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: center;
    background-color: var(--surface-color);
}

/* 待办事项卡片 */
.todos-list {
    padding: 15px 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    flex: 1;
}

.todo-item {
    display: flex;
    align-items: flex-start;
    position: relative;
    padding-right: 16px;
}

.todo-checkbox {
    margin-right: 12px;
    margin-top: 2px;
    color: var(--text-light);
    cursor: pointer;
}

.todo-content {
    flex: 1;
}

.todo-title {
    font-weight: 500;
    margin-bottom: 4px;
}

.todo-meta {
    font-size: 13px;
    color: var(--text-light);
}

.todo-priority {
    width: 4px;
    height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    border-radius: 2px;
}

.todo-priority.high {
    background: #f44336;
}

.todo-priority.medium {
    background: #ff9800;
}

.todo-priority.low {
    background: #4caf50;
}

.add-todo-btn {
    background: var(--secondary-color);
    color: var(--primary-color);
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
}

.add-todo-btn:hover {
    background: #bbdefb;
}

/* 活动时间线 */
.activity-timeline {
    padding: 18px 22px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    position: relative;
}

.activity-item:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 45px;
    left: 20px;
    width: 2px;
    height: calc(100% + 10px);
    background: linear-gradient(to bottom, var(--primary-alpha-30), rgba(100, 181, 246, 0.1));
    z-index: 0;
}

.activity-icon {
    width: 36px;
    height: 36px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 14px;
    color: var(--text-color);
    flex-shrink: 0;
    box-shadow: 0 4px 10px rgba(0,0,0,0.12);
    position: relative;
    z-index: 1;
}

.activity-icon.mail {
    background: linear-gradient(135deg, var(--primary-color), #64b5f6);
}

.activity-icon.update {
    background: linear-gradient(135deg, #4caf50, #81c784);
}

.activity-icon.ai {
    background: linear-gradient(135deg, #9c27b0, #ce93d8);
}

.activity-content {
    flex: 1;
    background-color: var(--background-color);
    padding: 14px 16px;
    border-radius: 12px;
    position: relative;
    box-shadow: 0 2px 6px rgba(0,0,0,0.04);
    border: 1px solid rgba(0,0,0,0.03);
}

.activity-content:before {
    content: '';
    position: absolute;
    left: -8px;
    top: 16px;
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid var(--background-color);
    z-index: 1;
}

.activity-content:after {
    content: '';
    position: absolute;
    left: -9px;
    top: 16px;
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid rgba(0,0,0,0.03);
    z-index: 0;
}

.activity-title {
    font-weight: 500;
    margin-bottom: 6px;
    line-height: 1.4;
    color: var(--text-color);
    font-size: 14px;
}

.activity-time {
    font-size: 12px;
    color: var(--text-light);
    display: flex;
    align-items: center;
}

.activity-time:before {
    content: '\2022'; /* 圆点符号 */
    margin-right: 4px;
    color: var(--primary-color);
    font-size: 14px;
}

.view-all-btn {
    background: var(--surface-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    padding: 12px 24px;
    border-radius: 10px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 3px 6px var(--shadow-color);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 200px;
    margin: 0 auto;
}

.view-all-btn:hover {
    background: var(--surface-hover);
    color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 6px 12px var(--shadow-hover);
}

.view-all-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px var(--shadow-color);
}

/* 漏斗图 */
.funnel-card .card-header {
    padding-right: 10px;
}

.date-filter select {
    padding: 5px 8px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    font-size: 13px;
    background: var(--surface-color);
}

.funnel-chart {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    position: relative;
}

.funnel-stage {
    height: 40px;
    position: relative;
    margin-bottom: 4px;
}

.stage-bar {
    height: 100%;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 18px;
    color: var(--text-color);
    font-weight: 600;
    transition: all 0.5s ease;
    box-shadow: 0 3px 6px rgba(0,0,0,0.08);
    min-width: 180px; /* 确保最小宽度，防止文字换行 */
    white-space: nowrap; /* 防止文字换行 */
    overflow: visible; /* 允许内容超出进度条 */
    position: relative;
    z-index: 1;
}

.stage-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    z-index: -1;
}

.stage-bar:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.stage-label {
    display: flex;
    align-items: center;
    position: relative;
    z-index: 2; /* 确保文字始终显示在最上层 */
    font-size: 14px;
    letter-spacing: 0.2px;
}

.stage-label:before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(255,255,255,0.7);
    margin-right: 8px;
    flex-shrink: 0; /* 防止圆点缩小 */
    box-shadow: 0 0 0 2px rgba(255,255,255,0.3);
}

.stage-value {
    font-weight: 700;
    background-color: rgba(255,255,255,0.25);
    padding: 5px 12px;
    border-radius: 16px;
    position: relative;
    z-index: 2; /* 确保数值始终显示在最上层 */
    flex-shrink: 0; /* 防止数值缩小 */
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    font-size: 15px;
}

.funnel-conversion {
    padding: 20px 28px 28px;
    display: flex;
    justify-content: space-around;
    gap: 24px;
    border-top: 1px dashed  var(--border-color);
    margin-top: 15px;
}

.conversion-item {
    text-align: center;
    background-color: var(--background-color);
    padding: 18px;
    border-radius: 14px;
    flex: 1;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0,0,0,0.04);
    border: 1px solid rgba(0,0,0,0.03);
    position: relative;
    overflow: hidden;
}

.conversion-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), #64b5f6);
    opacity: 0.7;
}

.conversion-item:hover {
    background-color: var(--secondary-color);
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.08);
}

.conversion-label {
    font-size: 15px;
    color: var(--text-light);
    margin-bottom: 10px;
    font-weight: 500;
    letter-spacing: 0.2px;
}

.conversion-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    background: linear-gradient(90deg, var(--primary-color), #64b5f6);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    letter-spacing: -0.5px;
}



/* 响应式调整 */
@media (max-width: 768px) {
    .kpi-cards {
        grid-template-columns: 1fr;
    }

    .dashboard-main-content {
        flex-direction: column;
    }

    .dashboard-right-column {
        width: 100%;
    }

    .activity-icon {
        width: 32px;
        height: 32px;
        border-radius: 10px;
    }

    .activity-content {
        padding: 10px 12px;
    }

    .funnel-chart {
        padding: 15px;
    }

    .funnel-conversion {
        flex-direction: column;
        gap: 10px;
    }
}

/* 数据分析页面样式 */
.analytics-container {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.analytics-layout {
    display: flex;
    height: 100%;
}

/* 分析页面侧边栏 */
.analytics-sidebar {
    width: 220px;
    background-color: var(--surface-color);
    border-right: 1px solid var(--border-color);
    padding: 20px 0;
    flex-shrink: 0;
}

.analytics-nav-header {
    padding: 0 20px;
    margin-bottom: 8px;
    font-size: 12px;
    font-weight: 600;
    color: var(--text-light);
    text-transform: uppercase;
}

.analytics-nav {
    margin-bottom: 24px;
}

.analytics-nav-item {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    text-decoration: none;
    color: var(--text-color);
    font-size: 14px;
    transition: background-color 0.2s;
}

.analytics-nav-item:hover {
    background-color: var(--hover-bg);
}

.analytics-nav-item.active {
    background-color: var(--active-bg);
    color: var(--primary-color);
    font-weight: 500;
}

.analytics-nav-item i {
    margin-right: 10px;
    font-size: 18px;
}

/* 分析页面主内容 */
.analytics-main {
    flex: 1;
    padding: 20px;
    background-color: var(--background-color);
    overflow-y: auto;
}

.analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.analytics-filters {
    display: flex;
    gap: 12px;
    align-items: center;
}

.date-range-picker {
    display: flex;
    align-items: center;
    padding: 6px 12px;
    background-color: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
}

.date-range-picker i:first-child {
    margin-right: 6px;
    color: var(--primary-color);
}

.date-range-picker i:last-child {
    margin-left: 6px;
    color: var(--text-light);
}

.filter-select {
    padding: 6px 12px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: var(--surface-color);
    font-size: 14px;
}

/* KPI卡片样式 */
.analytics-kpi-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
}

.analytics-kpi-card {
    background-color: var(--surface-color);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 3px var(--black-alpha-10);
    display: flex;
    flex-direction: column;
}

.kpi-title {
    font-size: 14px;
    color: var(--text-light);
    margin-bottom: 10px;
}

.kpi-value-big {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 14px;
}

.kpi-comparison {
    font-size: 13px;
    display: flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 16px;
    width: fit-content;
}

.kpi-comparison.positive {
    background-color: var(--success-pale);
    color: var(--success-dark);
}

.kpi-comparison.negative {
    background-color: var(--error-pale);
    color: #c62828;
}

.kpi-comparison i {
    margin-right: 4px;
}

.kpi-period {
    margin-left: 6px;
    color: var(--text-light);
}

/* 图表区域样式 */
.analytics-charts {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
}

.analytics-chart-card {
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 1px 3px var(--black-alpha-10);
    display: flex;
    flex-direction: column;
}

.analytics-chart-card.large {
    grid-column: span 2;
}

.analytics-chart-card.medium {
    grid-column: span 1;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
}

.chart-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.chart-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.chart-select {
    padding: 6px 12px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: var(--gray-100);
    font-size: 13px;
}

.chart-action-btn {
    padding: 6px 12px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    background-color: var(--gray-100);
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
}

.chart-action-btn:hover {
    background-color: var(--hover-bg);
}

.chart-content {
    padding: 20px;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 图表占位符样式 */
.chart-placeholder {
    width: 100%;
    height: 100%;
    position: relative;
}

/* 柱状图 */
.bar-chart {
    display: flex;
    align-items: flex-end;
    justify-content: space-around;
    height: 250px;
    padding-top: 30px;
}

.chart-bar {
    width: 15%;
    background-color: var(--primary-color);
    border-radius: 6px 6px 0 0;
    position: relative;
    min-height: 10px;
    transition: height 0.5s ease;
}

.chart-bar::before {
    content: attr(data-value);
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
}

.chart-label {
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 13px;
    white-space: nowrap;
}

/* 折线图 */
.line-chart {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.line-chart-svg {
    width: 100%;
    height: 100%;
}

/* 饼图 */
.pie-chart {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.pie-container {
    width: 150px;
    height: 150px;
    position: relative;
    border-radius: 50%;
    overflow: hidden;
}

.pie-slice {
    position: absolute;
    width: 100%;
    height: 100%;
    transform-origin: 50% 50%;
    background: conic-gradient(var(--color) calc(var(--percentage) * 1%), transparent 0);
}

.pie-slice:nth-child(1) {
    clip-path: polygon(50% 50%, 50% 0%, 100% 0%, 100% 100%, 0% 100%, 0% 0%, 50% 0%);
}

.pie-slice:nth-child(2) {
    clip-path: polygon(50% 50%, 100% 0%, 100% 100%, 0% 100%, 0% 0%);
    transform: rotate(calc(var(--percentage-1, 45%) * 3.6deg));
}

.pie-slice:nth-child(3) {
    clip-path: polygon(50% 50%, 100% 0%, 100% 100%, 0% 100%, 0% 0%);
    transform: rotate(calc((var(--percentage-1, 45%) + var(--percentage-2, 25%)) * 3.6deg));
}

.pie-slice:nth-child(4) {
    clip-path: polygon(50% 50%, 100% 0%, 100% 100%, 0% 100%, 0% 0%);
    transform: rotate(calc((var(--percentage-1, 45%) + var(--percentage-2, 25%) + var(--percentage-3, 15%)) * 3.6deg));
}

.pie-legend {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.legend-item {
    display: flex;
    align-items: center;
    font-size: 13px;
    gap: 6px;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 3px;
}

/* 表格样式 */
.analytics-table {
    width: 100%;
    border-collapse: collapse;
}

.analytics-table th,
.analytics-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.analytics-table th {
    font-weight: 600;
    color: var(--text-light);
    font-size: 13px;
}

.analytics-table td {
    font-size: 14px;
}

.analytics-table tbody tr:hover {
    background-color: var(--hover-bg);
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .analytics-charts {
        grid-template-columns: 1fr;
    }

    .analytics-chart-card.medium {
        grid-column: span 1;
    }

    .analytics-chart-card.large {
        grid-column: span 1;
    }
}

@media (max-width: 768px) {
    .analytics-layout {
        flex-direction: column;
    }

    .analytics-sidebar {
        width: 100%;
        padding: 10px 0;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }

    .analytics-nav {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        padding: 0 10px;
    }

    .analytics-nav-item {
        flex: 1;
        justify-content: center;
        padding: 8px 10px;
    }

    .analytics-nav-item i {
        margin-right: 6px;
    }

    .analytics-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .analytics-filters {
        width: 100%;
        flex-wrap: wrap;
    }

    .kpi-cards {
        grid-template-columns: 1fr;
    }
}

/* 近期内容预览 (新增) */
.recent-content-preview {
    margin-top: 20px; /* 减少顶部间距 */
    margin-bottom: 30px; /* 增加底部间距 */
}

.recent-content-preview h4 {
    font-size: 20px; /* 增加字体大小 */
    margin-bottom: 24px; /* 增加底部间距 */
    color: var(--text-color);
    position: relative;
    padding-left: 18px; /* 增加左侧填充 */
    font-weight: 600;
    display: flex;
    align-items: center;
}

.recent-content-preview h4:before {
    content: '';
    position: absolute;
    left: 0;
    top: 4px;
    height: 20px; /* 增加高度 */
    width: 5px; /* 增加宽度 */
    background: linear-gradient(to bottom, var(--primary-color), #64b5f6);
    border-radius: 3px; /* 增加圆角 */
}

.content-item {
    display: flex;
    margin-bottom: 24px; /* 增加底部间距 */
    background: var(--surface-color);
    border-radius: 14px; /* 增加圆角 */
    overflow: hidden;
    border: 1px solid var(--border-light); /* 调整边框颜色 */
    box-shadow: 0 4px 12px rgba(0,0,0,0.08); /* 增强阴影 */
    transition: all 0.25s ease; /* 调整过渡效果 */
    position: relative;
    height: 140px; /* 增加高度 */
    max-width: 100%; /* 确保不超出父容器 */
}

.content-item:hover {
    transform: translateY(-5px); /* 增加悬停时上移距离 */
    box-shadow: 0 8px 20px rgba(0,0,0,0.12); /* 增强悬停时阴影 */
    border-color: #bbdefb; /* 调整悬停时边框颜色 */
}

.content-platform {
    padding: 4px 6px;
    color: var(--text-color);
    font-size: 10px;
    display: flex;
    align-items: center;
    gap: 3px;
    font-weight: 500;
    position: absolute; /* 改为绝对定位 */
    left: 0;
    top: 0;
    z-index: 1; /* 确保在图片上方但在内容下方 */
    border-radius: 6px 0 6px 0; /* 添加右下圆角 */
    box-shadow: 0 2px 4px rgba(0,0,0,0.2); /* 添加阴影 */
}

.content-platform.youtube {
    background-color: #FF0000;
}

.content-platform.instagram {
    background: linear-gradient(45deg, #405DE6, #5851DB, #833AB4, #C13584, #E1306C, #FD1D1D);
}

.content-thumbnail {
    width: 180px; /* 增加宽度 */
    height: 140px; /* 增加高度，与内容项高度一致 */
    overflow: hidden;
    position: relative;
    z-index: 0;
    flex-shrink: 0;
    border-right: 1px solid #f0f0f0; /* 添加右侧边框 */
}

.content-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease; /* 调整过渡效果 */
}

.content-item:hover .content-thumbnail img {
    transform: scale(1.08); /* 增加缩放比例 */
}

.content-info {
    flex: 1;
    padding: 16px 20px; /* 增加内边距 */
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    justify-content: space-between; /* 改为两端对齐，标题在上，元数据在下 */
    overflow: hidden;
    min-width: 0;
    background-color: var(--surface-color); /* 确保背景色 */
}

.content-title {
    font-size: 18px; /* 增加字体大小 */
    font-weight: 600;
    margin-bottom: 10px; /* 增加底部间距 */
    color: var(--text-color);
    line-height: 1.4; /* 增加行高 */
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 75px; /* 调整最大高度 */
    width: 100%;
}

.content-meta {
    font-size: 13px; /* 增加字体大小 */
    color: var(--text-secondary);
    display: flex;
    align-items: center; /* 居中对齐 */
    flex-wrap: wrap;
    gap: 6px 10px; /* 增加间距 */
    width: 100%;
    line-height: 1.4;
    padding-top: 10px; /* 添加顶部内边距 */
    border-top: 1px dashed  var(--border-color); /* 添加顶部边框 */
}

.content-meta span {
    background-color: var(--gray-50);
    padding: 3px 8px; /* 增加内边距 */
    border-radius: 4px; /* 增加圆角 */
    display: inline-flex;
    align-items: center;
    font-size: 12px; /* 增加字体大小 */
    box-shadow: 0 1px 3px rgba(0,0,0,0.05); /* 添加阴影 */
}

.content-meta:before {
    content: '\f04e';
    font-family: 'remixicon';
    color: var(--primary-color);
    font-size: 16px; /* 增加字体大小 */
    flex-shrink: 0;
    margin-right: 6px; /* 添加右侧间距 */
}

.load-more-content {
    text-align: center;
    margin-top: 20px;
}

.load-more-content button {
    background-color: var(--gray-50);
    border: 1px solid  var(--border-color);
    color: var(--primary-color);
    font-size: 14px;
    cursor: pointer;
    padding: 8px 20px;
    border-radius: 20px;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.load-more-content button:hover {
    background-color: var(--primary-pale);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.08);
}

/* 编辑商品弹窗样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--overlay);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(3px);
}

.modal-container {
    background-color: var(--surface-color);
    border-radius: 16px;
    width: 90%;
    max-width: 650px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    animation: modal-appear 0.3s ease;
    position: relative; /* 确保定位正确 */
}

@keyframes modal-appear {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--surface-color);
}

.modal-header h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.modal-header h2 i {
    margin-right: 10px;
    color: var(--primary-color);
}

.close-modal-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-modal-btn:hover {
    background-color: var(--surface-hover);
    color: var(--text-color);
}

.modal-content {
    padding: 28px;
    overflow-y: auto;
    max-height: calc(90vh - 140px);
    scrollbar-width: thin;
    scrollbar-color: var(--gray-400) transparent;
    width: 100%; /* 确保内容区域使用全部宽度 */
    box-sizing: border-box; /* 确保padding不会增加宽度 */
}

.modal-content::-webkit-scrollbar {
    width: 6px;
}

.modal-content::-webkit-scrollbar-track {
    background: transparent;
}

.modal-content::-webkit-scrollbar-thumb {
    background-color: var(--gray-400);
    border-radius: 6px;
}

.modal-footer {
    padding: 20px 28px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    background-color: var(--surface-color);
    box-shadow: 0 -4px 10px var(--shadow-color);
}

#edit-product-form {
    width: 100%;
    box-sizing: border-box;
}

.form-group {
    margin-bottom: 24px;
    width: 100%;
    box-sizing: border-box;
}

.form-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: var(--text-color);
    font-size: 15px;
}

.form-input, .form-textarea {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 15px;
    transition: all 0.2s ease;
    background-color: var(--surface-color);
    color: var(--text-color);
    box-shadow: inset 0 1px 3px var(--shadow-color);
}

.form-input:hover, .form-textarea:hover {
    border-color: var(--gray-400);
}

.form-input:focus, .form-textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px var(--shadow-focus, var(--primary-alpha-10));
    background-color: var(--surface-color);
}

.form-textarea {
    min-height: 120px;
    resize: vertical;
    line-height: 1.5;
}

.image-upload-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
    background-color: var(--surface-hover);
    padding: 20px;
    border-radius: 12px;
    border: 1px dashed var(--border-color);
}

.image-preview-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 10px;
}

#product-image-preview {
    width: 280px;
    height: 280px;
    object-fit: contain;
    border-radius: 10px;
    border: 2px solid var(--border-color);
    box-shadow: 0 4px 10px var(--black-alpha-10);
    transition: all 0.3s ease;
    background-color: var(--surface-color);
}

#product-image-preview:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 15px var(--shadow-hover);
}

.upload-actions {
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 12px;
    width: 100%;
}

.upload-btn {
    background-color: var(--surface-color);
    color: var(--primary-color);
    border: 1px solid var(--border-color);
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px var(--black-alpha-05);
    white-space: nowrap;
    max-width: 120px;
}

.upload-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--black-alpha-10);
}

.tag-input-container {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
}

.add-tag-btn {
    background-color: var(--primary-color);
    color: var(--text-inverse);
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px var(--primary-alpha-20);
    display: flex;
    align-items: center;
}

.add-tag-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--primary-alpha-30);
}

.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 16px;
    background-color: var(--surface-color);
    padding: 16px;
    border-radius: 10px;
    min-height: 60px;
    border: 1px solid var(--border-color);
}

.tag-item {
    background-color: var(--primary-pale);
    color: var(--primary-color);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 4px var(--primary-alpha-10);
    transition: all 0.2s ease;
}

.tag-item:hover {
    background-color: var(--primary-light);
    transform: translateY(-2px);
    box-shadow: 0 3px 6px var(--primary-alpha-15);
}

.tag-item i {
    cursor: pointer;
    font-size: 16px;
    transition: all 0.2s ease;
}

.tag-item i:hover {
    color: #e53935;
    transform: scale(1.2);
}

.stats-edit-container {
    display: flex;
    gap: 20px;
    background-color: var(--surface-color);
    padding: 20px;
    border-radius: 10px;
    border: 1px solid var(--border-color);
    margin-top: 10px;
}

.stat-edit-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--surface-color);
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 2px 6px var(--black-alpha-05);
    transition: all 0.2s ease;
}

.stat-edit-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 10px var(--black-alpha-10);
}

.stat-edit-item label {
    font-size: 14px;
    margin-bottom: 12px;
    color: var(--text-secondary);
    font-weight: 500;
    text-align: center;
}

/* 产品菜单样式 */
.product-menu-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: transparent;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.2s ease;
    margin-left: 8px;
    position: relative; /* 添加相对定位 */
    z-index: 9000; /* 增加z-index确保在较高层级，但低于菜单本身 */
}

.product-menu-btn:hover {
    background-color: var(--black-alpha-05);
    color: var(--text-color);
}

.product-menu {
    position: absolute;
    top: 40px;
    right: 0;
    background-color: var(--surface-color);
    border-radius: 8px;
    box-shadow: 0 4px 12px var(--black-alpha-15);
    width: 160px;
    z-index: 9999; /* 增加z-index确保在最上层 */
    overflow: visible;
    display: none;
    /* 确保菜单不受父元素overflow:hidden的影响 */
    transform: translateZ(0);
    pointer-events: auto;
}

.product-menu.show {
    display: block;
    animation: menu-appear 0.2s ease;
}

@keyframes menu-appear {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.product-menu-item {
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.product-menu-item:hover {
    background-color: var(--gray-50);
}

.product-menu-item i {
    font-size: 18px;
}

.product-menu-item.edit i {
    color: var(--primary-color);
}

.product-menu-item.delete i {
    color: #e53935;
}

.product-menu-item.delete {
    color: #e53935;
}

/* 这个样式已经在上面定义过，这里删除重复定义 */

/* 近期活动项样式优化 */
.activities-list {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    max-height: 60vh;
}

.activity-item-full {
    display: flex;
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-light);
    transition: all 0.2s ease;
    position: relative;
}

.activity-item-full:last-child {
    border-bottom: none;
}

.activity-item-full:hover {
    background-color: var(--background-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.05);
    z-index: 1;
}

.activity-icon.user-action {
    width: 42px;
    height: 42px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    color: var(--text-color);
    flex-shrink: 0;
    box-shadow: 0 4px 10px rgba(0,0,0,0.12);
    background: linear-gradient(135deg, var(--primary-color), #64b5f6);
}

.activity-icon.user-action.add {
    background: linear-gradient(135deg, #4caf50, #81c784);
}

.activity-icon.user-action.edit {
    background: linear-gradient(135deg, #ff9800, #ffb74d);
}

.activity-icon.user-action.confirm {
    background: linear-gradient(135deg, #9c27b0, #ce93d8);
}

.activity-icon.user-action.ai {
    background: linear-gradient(135deg, #9c27b0, #ce93d8);
}

.activity-content-full {
    flex: 1;
    background-color: var(--background-color);
    padding: 16px;
    border-radius: 12px;
    position: relative;
    box-shadow: 0 2px 6px rgba(0,0,0,0.04);
    border: 1px solid rgba(0,0,0,0.03);
}

.activity-header-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    border-bottom: 1px dashed rgba(0,0,0,0.08);
    padding-bottom: 8px;
}

.activity-title-full {
    font-weight: 600;
    color: var(--text-color);
    font-size: 15px;
    line-height: 1.4;
    display: flex;
    align-items: center;
}

.activity-type-badge {
    display: inline-flex;
    align-items: center;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    margin-right: 8px;
    color: var(--text-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.activity-type-badge.email {
    background-color: var(--primary-color);
}

.activity-type-badge.add {
    background-color: #4caf50;
}

.activity-type-badge.edit {
    background-color: #ff9800;
}

.activity-type-badge.confirm {
    background-color: #9c27b0;
}

.activity-type-badge.ai {
    background-color: #9c27b0;
}

.activity-time-full {
    font-size: 13px;
    color: var(--text-light);
    white-space: nowrap;
    margin-left: 10px;
    display: flex;
    align-items: center;
    background-color: rgba(0,0,0,0.04);
    padding: 4px 10px;
    border-radius: 16px;
}

.activity-time-full i {
    margin-right: 5px;
    font-size: 14px;
    color: var(--text-secondary);
}

.activity-details {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.activity-product, .activity-summary {
    display: flex;
    align-items: center;
}

.activity-product i, .activity-summary i {
    margin-right: 8px;
    color: var(--primary-color);
    font-size: 16px;
    opacity: 0.8;
}

.activity-actions {
    display: flex;
    justify-content: flex-end;
}

.activity-actions .action-btn {
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
    background-color: var(--surface-color);
    border: 1px solid  var(--border-color);
    color: var(--text-secondary);
}

.activity-actions .action-btn:hover {
    background-color: var(--info-bg);
    color: var(--primary-color);
    border-color: #bbdefb;
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0,0,0,0.1);
}

/* 产品信息卡片样式 */
.product-info-card {
    display: flex;
    align-items: flex-start;
    gap: 16px; /* 减小间距 */
    background: linear-gradient(135deg, var(--surface-color) 0%, rgba(255, 255, 255, 0.95) 100%);
    border: 1px solid var(--border-light);
    border-radius: 12px; /* 减小圆角 */
    padding: 16px; /* 减小内边距 */
    margin: var(--spacing-sm) auto; /* 居中对齐 */
    max-width: 800px; /* 与商品分析卡片保持一致 */
    width: 100%;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06); /* 减小阴影 */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

/* 商品信息补全卡片样式 */
.product-completion-card {
    background: linear-gradient(135deg, var(--surface-color) 0%, rgba(255, 255, 255, 0.95) 100%);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    padding: 20px;
    margin: var(--spacing-sm) auto;
    max-width: 800px;
    width: 100%;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.product-completion-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0.8;
}

.completion-card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-light);
}

.completion-card-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.completion-card-header i {
    color: var(--primary-color);
    font-size: 20px;
}

.completion-form {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-field label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.form-field label .required {
    color: #ef4444;
    font-size: 12px;
}

.form-field input,
.form-field textarea {
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 14px;
    background: var(--background-color);
    color: var(--text-primary);
    transition: all 0.3s ease;
    resize: vertical;
}

.form-field input:focus,
.form-field textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-field.error input,
.form-field.error textarea {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-field textarea {
    min-height: 80px;
    max-height: 120px;
}

.completion-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid var(--border-light);
}

.completion-info {
    font-size: 13px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 6px;
}

.completion-info i {
    color: var(--primary-color);
}

.submit-completion-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.submit-completion-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.submit-completion-btn:disabled {
    background: var(--border-color);
    cursor: not-allowed;
    transform: none;
}

.error-message {
    color: #ef4444;
    font-size: 13px;
    margin-top: 16px;
    padding: 12px;
    background: rgba(239, 68, 68, 0.1);
    border-radius: 6px;
    border-left: 3px solid #ef4444;
    display: none;
}

.product-info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-info-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
}

.product-info-card:hover::before {
    opacity: 1;
}

/* 商品图片容器 */
.product-image-container {
    position: relative;
    width: 140px;
    height: 140px;
    border-radius: 16px;
    overflow: hidden;
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-50) 100%);
    flex-shrink: 0;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.8);
}

.product-image-loader {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: var(--gray-100);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    gap: var(--spacing-xs);
}

.image-loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid var(--gray-300);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--radius-base);
    transition: transform var(--animation-duration) var(--animation-easing);
}

.product-info-card:hover .product-image {
    transform: scale(1.05);
}

/* 商品基本信息 */
.product-basic-info {
    flex: 1;
    min-width: 0;
}

.product-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.product-title .success-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, var(--success-color) 0%, #10b981 100%);
    color: white;
    border-radius: 50%;
    font-size: 16px;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 6px 16px rgba(16, 185, 129, 0.4);
    }
}

.product-name {
    font-size: 22px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 12px;
    line-height: 1.3;
    background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-color) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.product-category-tag {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: linear-gradient(135deg, var(--primary-alpha-10) 0%, rgba(59, 130, 246, 0.08) 100%);
    color: var(--primary-color);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    border: 1px solid var(--primary-alpha-20);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.product-quick-stats {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.product-simple-stats {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    align-items: center;
}

.price-info, .rating-info {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: linear-gradient(135deg, var(--gray-50) 0%, rgba(248, 250, 252, 0.8) 100%);
    color: var(--text-primary);
    padding: 6px 12px;
    border-radius: 16px;
    font-size: var(--font-size-sm);
    font-weight: 600;
    border: 1px solid var(--border-light);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.price-info {
    background: linear-gradient(135deg, var(--success-alpha-10) 0%, rgba(16, 185, 129, 0.08) 100%);
    color: var(--success-color);
    border-color: var(--success-alpha-20);
}

.rating-info {
    background: linear-gradient(135deg, var(--warning-alpha-10) 0%, rgba(245, 158, 11, 0.08) 100%);
    color: var(--warning-color);
    border-color: var(--warning-alpha-20);
}

.price-info i, .rating-info i {
    font-size: 14px;
}

.rating-info i {
    color: #fbbf24;
}

.price-range,
.target-market {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    background: linear-gradient(135deg, var(--surface-hover) 0%, rgba(248, 250, 252, 0.8) 100%);
    padding: 10px 16px;
    border-radius: 12px;
    border: 1px solid var(--border-light);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.price-range:hover,
.target-market:hover {
    background: linear-gradient(135deg, var(--primary-alpha-05) 0%, rgba(59, 130, 246, 0.05) 100%);
    border-color: var(--primary-alpha-20);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 特征分析样式 */
.feature-analysis {
    margin-top: var(--spacing-sm);
}

.core-features {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-base);
}

.feature-tag {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-base);
    border-radius: var(--radius-base);
    border-left: 4px solid;
    font-size: var(--font-size-sm);
    font-weight: 500;
    transition: all var(--animation-duration) var(--animation-easing);
    cursor: default;
}

.feature-tag:hover {
    transform: translateX(4px);
    box-shadow: 0 2px 6px var(--black-alpha-08);
}

.feature-tag.ai-feature {
    background: var(--primary-alpha-05);
    border-color: var(--primary-color);
    color: var(--primary-dark);
}

.feature-tag.quality-feature {
    background: var(--success-alpha-10);
    border-color: var(--success-color);
    color: var(--success-dark);
}

.feature-tag.battery-feature {
    background: var(--warning-alpha-10);
    border-color: var(--warning-color);
    color: var(--warning-dark);
}

.feature-tag.durability-feature {
    background: var(--info-alpha-10);
    border-color: var(--info-color);
    color: var(--info-dark);
}

.feature-tag.health-feature {
    background: #ffeaa7;
    border-color: #fdcb6e;
    color: #e17055;
}

.feature-tag.fitness-feature {
    background: #a8e6cf;
    border-color: #00b894;
    color: #00a085;
}

.feature-tag.smart-feature {
    background: #ddd6fe;
    border-color: #8b5cf6;
    color: #7c3aed;
}

/* 目标受众分析 */
.target-audience {
    margin-top: var(--spacing-base);
}

.target-audience h5 {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.audience-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.audience-tag {
    background: var(--gray-100);
    color: var(--text-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
    border: 1px solid var(--border-light);
    transition: all var(--animation-duration) var(--animation-easing);
}

.audience-tag:hover {
    background: var(--primary-alpha-10);
    color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* 匹配进度样式 */
.matching-progress {
    margin-top: var(--spacing-sm);
}

/* 中央输入框按钮布局样式 */
.input-wrapper-with-buttons {
    position: relative;
}

.input-buttons-overlay {
    /* 容器不再需要定位，按钮直接使用绝对定位 */
    display: contents;
}

.circular-plus-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
    position: absolute;
    bottom: 8px;
    right: 56px;
    z-index: 20;
}

.circular-plus-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.circular-plus-btn:active {
    transform: scale(0.95);
}

/* 重复的central-send-button样式已移除，使用上面的主要定义 */

/* 确保输入框有足够的右侧内边距，为按钮留出空间 */
.input-wrapper-with-buttons .central-input {
    padding-right: 100px;
    resize: none;
    overflow: hidden;
}

/* 图片上传字段样式 */
.image-upload-field {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.upload-options {
    display: flex;
    gap: 12px;
    align-items: center;
}

.upload-option {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.upload-option label {
    font-size: 13px;
    color: var(--text-secondary);
    font-weight: 500;
}

.file-upload-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
}

.file-upload-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px;
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    background: var(--background-color);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.file-upload-button:hover {
    border-color: var(--primary-color);
    background: var(--primary-alpha-05);
    color: var(--primary-color);
}

.image-preview-container {
    margin-top: 12px;
    display: none;
}

.image-preview {
    max-width: 200px;
    max-height: 150px;
    border-radius: 8px;
    border: 1px solid var(--border-light);
    object-fit: cover;
}

.preview-actions {
    margin-top: 8px;
    display: flex;
    gap: 8px;
}

.preview-action-btn {
    padding: 4px 8px;
    font-size: 12px;
    border: 1px solid var(--border-color);
    background: var(--background-color);
    color: var(--text-secondary);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.preview-action-btn:hover {
    background: var(--surface-hover);
    color: var(--text-primary);
}

.preview-action-btn.remove {
    color: #ef4444;
    border-color: #ef4444;
}

.preview-action-btn.remove:hover {
    background: rgba(239, 68, 68, 0.1);
}

.progress-info {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.progress-info strong {
    color: var(--primary-color);
    font-weight: 600;
}

.progress-bar-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: auto; /* 推到底部 */
}

.progress-bar {
    flex: 1;
    height: 10px; /* 稍微增加高度 */
    background: var(--gray-200);
    border-radius: var(--radius-sm);
    overflow: hidden;
    min-width: 300px; /* 设置最小宽度，让进度条更长 */
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1); /* 添加内阴影 */
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-sm);
    width: 0%;
    transition: width 0.1s ease-out;
    position: relative;
    overflow: hidden;
}

.progress-fill.smartwatch-progress {
    width: 0%;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--primary-color);
    min-width: 80px;
    text-align: right;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .product-info-card {
        flex-direction: column;
        text-align: center;
        padding: 20px;
        gap: 16px;
    }

    .product-image-container {
        width: 120px;
        height: 120px;
        align-self: center;
        margin-bottom: 8px;
    }

    .product-name {
        font-size: 20px;
    }

    .product-quick-stats,
    .product-simple-stats {
        justify-content: center;
    }
    
    .core-features {
        gap: var(--spacing-xs);
    }
    
    .feature-tag {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-xs);
    }
    
    .progress-bar-container {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
    
    .progress-text {
        text-align: center;
        min-width: auto;
    }
}

/* 加载状态优化 */
.product-image-loader i {
    font-size: 24px;
    color: var(--gray-400);
    margin-bottom: var(--spacing-xs);
}

/* 错误状态样式 */
.product-image-container .error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--gray-500);
    font-size: var(--font-size-xs);
}

.product-image-container .error-state i {
    font-size: 32px;
    margin-bottom: var(--spacing-xs);
    color: var(--gray-400);
}

/* 分析进度和推理链样式 */
.analyzing-progress,
.feature-analysis-progress,
.feature-extraction-progress,
.blogger-matching-progress,
.email-generation-progress {
    background-color: var(--background-color);
    border-radius: 12px;
    padding: 20px;
    margin: 10px auto; /* 居中对齐 */
    border: 1px solid var(--border-color);
    min-height: 180px; /* 统一最小高度 */
    max-width: 750px; /* 调整最大宽度 */
    width: 100%; /* 统一宽度 */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.analysis-chain,
.extraction-chain,
.matching-chain,
.email-generation-chain {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 15px 0;
    position: relative;
    flex: 1; /* 让推理链占据剩余空间 */
}

.analysis-chain::before,
.extraction-chain::before,
.matching-chain::before,
.email-generation-chain::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(to right, var(--border-color) 0%, var(--border-color) 100%);
    z-index: 1;
}

/* 邮件生成推理链特殊样式 - 4个步骤 */
.email-generation-chain .chain-step {
    flex: 1;
    max-width: 22%; /* 4个步骤，每个占22%，留出间距 */
}

.chain-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.chain-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--gray-300);
    border: 2px solid var(--background-color);
    transition: all 0.3s ease;
}

.chain-step.active .chain-dot {
    background-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(var(--primary-color-rgb), 0.2);
}

.chain-step span {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    text-align: center;
    white-space: nowrap;
    transition: color 0.3s ease;
}

.chain-step.active span {
    color: var(--primary-color);
    font-weight: 600;
}

/* 特征提取卡片样式 */
.feature-extraction-card {
    background-color: var(--background-color);
    border-radius: 12px;
    padding: 20px;
    margin: 10px auto; /* 居中对齐 */
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: 850px; /* 调整宽度与商品分析卡片协调 */
    width: 100%; /* 统一宽度 */
}

.feature-card-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.feature-card-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    display: flex;
    align-items: center;
    gap: 8px;
}

.feature-card-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.section-title {
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.core-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.feature-tag.health-feature {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4caf50;
    border: 1px solid rgba(76, 175, 80, 0.2);
}

.feature-tag.tech-feature {
    background-color: rgba(33, 150, 243, 0.1);
    color: #2196f3;
    border: 1px solid rgba(33, 150, 243, 0.2);
}

.feature-tag.smart-feature {
    background-color: rgba(156, 39, 176, 0.1);
    color: #9c27b0;
    border: 1px solid rgba(156, 39, 176, 0.2);
}

.feature-tag.portable-feature {
    background-color: rgba(255, 152, 0, 0.1);
    color: #ff9800;
    border: 1px solid rgba(255, 152, 0, 0.2);
}

.audience-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.audience-tag {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background-color: var(--gray-100);
    border-radius: 20px;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.audience-tag.business {
    background-color: rgba(63, 81, 181, 0.1);
    color: #3f51b5;
    border-color: rgba(63, 81, 181, 0.2);
}

.audience-tag.travel {
    background-color: rgba(0, 150, 136, 0.1);
    color: #009688;
    border-color: rgba(0, 150, 136, 0.2);
}

.audience-tag.tech {
    background-color: rgba(96, 125, 139, 0.1);
    color: #607d8b;
    border-color: rgba(96, 125, 139, 0.2);
}

.audience-tag.student {
    background-color: rgba(233, 30, 99, 0.1);
    color: #e91e63;
    border-color: rgba(233, 30, 99, 0.2);
}

.marketing-points {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.marketing-point {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 8px 0;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.marketing-point i {
    color: var(--success-color);
    margin-top: 2px;
}

/* 营销活动设置样式 */
.campaign-settings-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.campaign-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 15px;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-group label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 4px;
}

.required {
    color: var(--error-color);
    font-size: var(--font-size-xs);
}

.campaign-input {
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: var(--font-size-sm);
    background-color: var(--surface-color);
    color: var(--text-primary);
    transition: all 0.2s ease;
}

.campaign-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.campaign-input:invalid {
    border-color: var(--error-color);
}

.input-hint {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-top: 4px;
}

.feature-card-actions {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: center;
}

.start-matching-btn {
    padding: 12px 24px;
    font-size: var(--font-size-md);
    font-weight: 600;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.start-matching-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.3);
}

/* 可编辑标签区域样式 */
.editable-section {
    position: relative;
    margin-bottom: 20px;
}

.section-title-with-edit {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.edit-section-btn {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 4px 8px;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.edit-section-btn:hover {
    background-color: var(--gray-50);
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.tags-editor {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.existing-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 12px;
    min-height: 24px;
}

.tag-item {
    background: var(--gray-100);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    border: 1px solid var(--border-light);
    color: var(--text-primary);
}

.remove-tag-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    transition: all 0.2s ease;
}

.remove-tag-btn:hover {
    background-color: var(--error-color);
    color: white;
}

.input-container {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.tag-input {
    flex: 1;
    padding: 6px 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 12px;
    background-color: var(--background-color);
    color: var(--text-primary);
    transition: all 0.2s ease;
}

.tag-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
}

.char-counter {
    font-size: 10px;
    color: var(--text-secondary);
    min-width: 30px;
    text-align: right;
    font-weight: 500;
}

.editor-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.primary-btn-small,
.secondary-btn-small {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
}

.primary-btn-small {
    background-color: var(--primary-color);
    color: white;
}

.primary-btn-small:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

.secondary-btn-small {
    background-color: var(--gray-100);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.secondary-btn-small:hover {
    background-color: var(--gray-200);
    color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .analysis-chain,
    .extraction-chain,
    .matching-chain,
    .email-generation-chain {
        flex-direction: column;
        gap: 15px;
    }

    .analysis-chain::before,
    .extraction-chain::before,
    .matching-chain::before,
    .email-generation-chain::before {
        display: none;
    }

    .progress-bar {
        min-width: 250px; /* 移动端稍微缩短 */
    }

    /* 移动端卡片宽度优化 */
    .analyzing-progress,
    .feature-analysis-progress,
    .feature-extraction-progress,
    .blogger-matching-progress,
    .email-generation-progress,
    .product-analysis-card,
    .product-analysis-card-compact,
    .feature-extraction-card,
    .product-info-card,
    .analysis-steps,
    .analysis-complete,
    .creators-recommendation-container,
    .email-generation-container,
    .email-sent-success,
    .ai-message .message-content {
        max-width: 100%;
        margin-left: 0;
        margin-right: 0;
    }

    /* 移动端博主切换功能优化 */
    .creator-switch-section {
        padding: 12px;
        margin-bottom: 15px;
    }

    .creator-switch-container {
        gap: 6px;
    }

    .creator-switch-option {
        padding: 6px 12px;
        font-size: 13px;
        border-radius: 16px;
    }

    .creator-switch-option:hover {
        transform: none; /* 移动端禁用hover动画 */
    }

    .core-features {
        grid-template-columns: 1fr;
    }

    .audience-tags {
        justify-content: center;
    }

    .chain-step span {
        white-space: normal;
        text-align: center;
    }

    /* 商品分析卡片移动端优化 */
    .product-analysis-card {
        margin: 12px 0;
        border-radius: 12px;
        max-width: 100%; /* 移动端全宽 */
    }

    .product-analysis-header,
    .product-analysis-content,
    .product-analysis-actions {
        padding: 16px;
    }

    .product-info-item {
        flex-direction: column;
        gap: 8px;
        padding: 12px;
    }

    .info-label {
        width: auto;
        font-size: 14px;
        margin-bottom: 4px;
    }

    .product-analysis-actions {
        flex-direction: column;
        gap: 12px;
    }

    .primary-btn,
    .edit-product-btn {
        width: 100%;
        justify-content: center;
        padding: 14px 20px;
    }

    /* 紧凑型商品分析卡片移动端优化 */
    .product-analysis-card-compact {
        margin: 8px 0;
        max-width: 100%;
    }

    .product-info-row {
        flex-direction: column;
        gap: 8px;
    }

    .info-item-compact.full-width {
        flex: 1;
    }

    .product-analysis-actions-compact {
        flex-direction: column;
        gap: 8px;
        padding: 8px 10px;
    }

    .primary-btn-compact,
    .edit-product-btn-compact {
        width: 100%;
        justify-content: center;
        padding: 8px 16px;
    }

    /* 营销活动设置移动端优化 */
    .campaign-inputs {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .campaign-input {
        padding: 12px;
        font-size: var(--font-size-md);
    }

    /* 可编辑标签移动端优化 */
    .section-title-with-edit {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .edit-section-btn {
        align-self: flex-end;
        padding: 6px 10px;
        font-size: 11px;
    }

    .tags-editor {
        padding: 10px;
    }

    .existing-tags {
        margin-bottom: 10px;
    }

    .tag-item {
        font-size: 11px;
        padding: 3px 6px;
    }

    .input-container {
        flex-direction: column;
        align-items: stretch;
        gap: 6px;
        margin-bottom: 10px;
    }

    .tag-input {
        padding: 8px;
        font-size: 14px;
    }

    .char-counter {
        align-self: flex-end;
        font-size: 11px;
    }

    .editor-actions {
        flex-direction: column;
        gap: 6px;
    }

    .primary-btn-small,
    .secondary-btn-small {
        padding: 8px 12px;
        font-size: 13px;
        width: 100%;
        justify-content: center;
    }
}